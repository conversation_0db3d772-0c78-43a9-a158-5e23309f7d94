package com.ruoyi.vm_car_order.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * vm_car_order对象 vw_car_order
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
public class VwCarOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private String id;


    private Date startTime;
    private Date endTime;



    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 申请编号（查询贷款人 联系电话 出单渠道 业务员 车牌号码 车辆位置 车辆状态 GPS状态） */
    @Excel(name = "申请编号", readConverterExp = "查=询贷款人,联=系电话,出=单渠道,业=务员,车=牌号码,车=辆位置,车=辆状态,G=PS状态")
    private String applyNo;

    /** 找车团队（接单团队

） */
    @Excel(name = "找车团队", readConverterExp = "接=单团队")
    private Long teamId;

    /** 车库id */
    @Excel(name = "车库id")
    private Long garageId;

    /** 1-入库，2-出库 */
    @Excel(name = "1-入库，2-出库")
    private Integer libraryStatus;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入库时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inboundTime;

    /** 出库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出库时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date outboundTime;

    /** 找车佣金 */
    @Excel(name = "找车佣金")
    private BigDecimal locatingCommission;

    /** 钥匙状态：0-未收回，1-已邮寄，2-已收回，3-未归还 */
    @Excel(name = "钥匙状态：0-未收回，1-已邮寄，2-已收回，3-未归还")
    private Long keyStatus;

    /** 钥匙邮寄时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "钥匙邮寄时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date keyTime;

    /** 收车方式：1-主动交车，2-钥匙开车，3-板车拖车 */
    @Excel(name = "收车方式：1-主动交车，2-钥匙开车，3-板车拖车")
    private Integer collectionMethod;

    /** 订单状态，0-发起，1-已分配，2-已完成，3-未完成，4-已撤销 */
    @Excel(name = "订单状态，0-发起，1-已分配，2-已完成，3-未完成，4-已撤销")
    private Integer status;

    /** 分配时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "分配时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date allocationTime;

    /** 钥匙所在地-省 */
    @Excel(name = "钥匙所在地-省")
    private String keyProvince;

    /** 钥匙所在地-市 */
    @Excel(name = "钥匙所在地-市")
    private String keyCity;

    /** 钥匙所在地-区 */
    @Excel(name = "钥匙所在地-区")
    private String keyBorough;

    /** 钥匙所在地-地址 */
    @Excel(name = "钥匙所在地-地址")
    private String keyAddress;

    /** 客户名称 */
    @Excel(name = "客户名称")
    private String customerName;

    /** 客户ID */
    @Excel(name = "客户ID")
    private String customerId;

    /** 贷款ID */
    @Excel(name = "贷款ID")
    private String loanId;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String mobilePhone;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String plateNo;

    /** 名称 */
    @Excel(name = "名称")
    private String jgName;

    /** 1-直营 2-外部 */
    @Excel(name = "1-直营 2-外部")
    private String jgStatus;

    /** 车所在地-详细地址 */
    @Excel(name = "车所在地-详细地址")
    private String carDetailAddress;

    /** 车辆状态 */
    @Excel(name = "车辆状态")
    private String carStatus;

    /** GPS状态 */
    @Excel(name = "GPS状态")
    private String gpsStatus;

    /** 团队名称 */
    @Excel(name = "团队名称")
    private String teamName;

    /** 车库名称 */
    @Excel(name = "车库名称")
    private String garageName;

    /** 逾期状态 */
    private String slippageStatus;

    /** 逾期金额 */
    private java.math.BigDecimal overdueAmt;

    /** 派单员 1-贷后文员 2-法诉文员 */
    private Integer dispatcher;

//    public void setId(String id)
//    {
//        this.id = id;
//    }
//
//    public String getId()
//    {
//        return id;
//    }
//
//    public void setCreateDate(Date createDate)
//    {
//        this.createDate = createDate;
//    }
//
//    public Date getCreateDate()
//    {
//        return createDate;
//    }
//
//    public void setUpdateDate(Date updateDate)
//    {
//        this.updateDate = updateDate;
//    }
//
//    public Date getUpdateDate()
//    {
//        return updateDate;
//    }
//
//    public void setApplyNo(String applyNo)
//    {
//        this.applyNo = applyNo;
//    }
//
//    public String getApplyNo()
//    {
//        return applyNo;
//    }
//
//    public void setTeamId(Long teamId)
//    {
//        this.teamId = teamId;
//    }
//
//    public Long getTeamId()
//    {
//        return teamId;
//    }
//
//    public void setGarageId(Long garageId)
//    {
//        this.garageId = garageId;
//    }
//
//    public Long getGarageId()
//    {
//        return garageId;
//    }
//
//    public void setLibraryStatus(Integer libraryStatus)
//    {
//        this.libraryStatus = libraryStatus;
//    }
//
//    public Integer getLibraryStatus()
//    {
//        return libraryStatus;
//    }
//
//    public void setInboundTime(Date inboundTime)
//    {
//        this.inboundTime = inboundTime;
//    }
//
//    public Date getInboundTime()
//    {
//        return inboundTime;
//    }
//
//    public void setOutboundTime(Date outboundTime)
//    {
//        this.outboundTime = outboundTime;
//    }
//
//    public Date getOutboundTime()
//    {
//        return outboundTime;
//    }
//
//    public void setLocatingCommission(BigDecimal locatingCommission)
//    {
//        this.locatingCommission = locatingCommission;
//    }
//
//    public BigDecimal getLocatingCommission()
//    {
//        return locatingCommission;
//    }
//
//    public void setKeyStatus(Long keyStatus)
//    {
//        this.keyStatus = keyStatus;
//    }
//
//    public Long getKeyStatus()
//    {
//        return keyStatus;
//    }
//
//    public void setKeyTime(Date keyTime)
//    {
//        this.keyTime = keyTime;
//    }
//
//    public Date getKeyTime()
//    {
//        return keyTime;
//    }
//
//    public void setCollectionMethod(Integer collectionMethod)
//    {
//        this.collectionMethod = collectionMethod;
//    }
//
//    public Integer getCollectionMethod()
//    {
//        return collectionMethod;
//    }
//
//    public void setStatus(Integer status)
//    {
//        this.status = status;
//    }
//
//    public Integer getStatus()
//    {
//        return status;
//    }
//
//    public void setAllocationTime(Date allocationTime)
//    {
//        this.allocationTime = allocationTime;
//    }
//
//    public Date getAllocationTime()
//    {
//        return allocationTime;
//    }
//
//    public void setKeyProvince(String keyProvince)
//    {
//        this.keyProvince = keyProvince;
//    }
//
//    public String getKeyProvince()
//    {
//        return keyProvince;
//    }
//
//    public void setKeyCity(String keyCity)
//    {
//        this.keyCity = keyCity;
//    }
//
//    public String getKeyCity()
//    {
//        return keyCity;
//    }
//
//    public void setKeyBorough(String keyBorough)
//    {
//        this.keyBorough = keyBorough;
//    }
//
//    public String getKeyBorough()
//    {
//        return keyBorough;
//    }
//
//    public void setKeyAddress(String keyAddress)
//    {
//        this.keyAddress = keyAddress;
//    }
//
//    public String getKeyAddress()
//    {
//        return keyAddress;
//    }
//
//    public void setCustomerName(String customerName)
//    {
//        this.customerName = customerName;
//    }
//
//    public String getCustomerName()
//    {
//        return customerName;
//    }
//
//    public void setMobilePhone(String mobilePhone)
//    {
//        this.mobilePhone = mobilePhone;
//    }
//
//    public String getMobilePhone()
//    {
//        return mobilePhone;
//    }
//
//    public void setNickName(String nickName)
//    {
//        this.nickName = nickName;
//    }
//
//    public String getNickName()
//    {
//        return nickName;
//    }
//
//    public void setPlateNo(String plateNo)
//    {
//        this.plateNo = plateNo;
//    }
//
//    public String getPlateNo()
//    {
//        return plateNo;
//    }
//
//    public void setJgName(String jgName)
//    {
//        this.jgName = jgName;
//    }
//
//    public String getJgName()
//    {
//        return jgName;
//    }
//
//    public void setJgStatus(String jgStatus)
//    {
//        this.jgStatus = jgStatus;
//    }
//
//    public String getJgStatus()
//    {
//        return jgStatus;
//    }
//
//    public void setCarDetailAddress(String carDetailAddress)
//    {
//        this.carDetailAddress = carDetailAddress;
//    }
//
//    public String getCarDetailAddress()
//    {
//        return carDetailAddress;
//    }
//
//    public void setCarStatus(String carStatus)
//    {
//        this.carStatus = carStatus;
//    }
//
//    public String getCarStatus()
//    {
//        return carStatus;
//    }
//
//    public void setGpsStatus(String gpsStatus)
//    {
//        this.gpsStatus = gpsStatus;
//    }
//
//    public String getGpsStatus()
//    {
//        return gpsStatus;
//    }
//
//    public void setTeamName(String teamName)
//    {
//        this.teamName = teamName;
//    }
//
//    public String getTeamName()
//    {
//        return teamName;
//    }
//
//    public void setGarageName(String garageName)
//    {
//        this.garageName = garageName;
//    }
//
//    public String getGarageName()
//    {
//        return garageName;
//    }
//
//    @Override
//    public String toString() {
//        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
//            .append("id", getId())
//            .append("createBy", getCreateBy())
//            .append("createDate", getCreateDate())
//            .append("updateBy", getUpdateBy())
//            .append("updateDate", getUpdateDate())
//            .append("applyNo", getApplyNo())
//            .append("teamId", getTeamId())
//            .append("garageId", getGarageId())
//            .append("libraryStatus", getLibraryStatus())
//            .append("inboundTime", getInboundTime())
//            .append("outboundTime", getOutboundTime())
//            .append("locatingCommission", getLocatingCommission())
//            .append("keyStatus", getKeyStatus())
//            .append("keyTime", getKeyTime())
//            .append("collectionMethod", getCollectionMethod())
//            .append("status", getStatus())
//            .append("allocationTime", getAllocationTime())
//            .append("keyProvince", getKeyProvince())
//            .append("keyCity", getKeyCity())
//            .append("keyBorough", getKeyBorough())
//            .append("keyAddress", getKeyAddress())
//            .append("customerName", getCustomerName())
//            .append("mobilePhone", getMobilePhone())
//            .append("nickName", getNickName())
//            .append("plateNo", getPlateNo())
//            .append("jgName", getJgName())
//            .append("jgStatus", getJgStatus())
//            .append("carDetailAddress", getCarDetailAddress())
//            .append("carStatus", getCarStatus())
//            .append("gpsStatus", getGpsStatus())
//            .append("teamName", getTeamName())
//            .append("garageName", getGarageName())
//            .toString();
//    }
}
