09:45:37.460 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:45:37.469 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23256 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:45:37.471 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:45:45.747 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:45:45.749 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:45:45.750 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:45:45.833 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:45:58.652 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:45:59.358 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:46:03.086 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:46:03.093 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:46:03.094 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:46:03.094 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:46:03.095 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:46:03.095 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:46:03.096 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:46:03.096 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@167fc0bd
09:46:03.194 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:46:03.194 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:46:03.197 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:46:04.030 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:46:04.379 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:46:04.390 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 27.313 seconds (JVM running for 28.071)
09:46:04.392 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:46:04.392 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:46:04.393 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:46:04.393 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:46:04.394 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:46:04.394 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:46:04.394 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:46:04.394 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:46:04.395 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:46:04.395 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:47:50.463 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 24300 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:47:50.467 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:47:50.467 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:47:52.932 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:47:52.935 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:47:52.935 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:47:53.025 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:48:05.847 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:48:06.565 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:48:10.232 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:48:10.243 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:48:10.244 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:48:10.245 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:48:10.245 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:48:10.246 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:48:10.246 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:48:10.247 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@341678a6
09:48:10.367 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:48:10.368 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:48:10.389 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:48:11.421 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:48:11.812 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:48:11.825 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.709 seconds (JVM running for 22.234)
09:48:11.831 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:48:11.831 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:48:11.832 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:48:11.832 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:48:11.833 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:48:11.834 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:48:11.834 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:48:11.834 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:48:11.834 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:48:11.834 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:48:26.737 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25252 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:48:26.745 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:48:26.751 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:48:29.347 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:48:29.350 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:48:29.350 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:48:29.443 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:48:31.213 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:48:31.947 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:48:45.870 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:48:45.879 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:48:45.879 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:48:45.880 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:48:45.881 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:48:45.881 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:48:45.881 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:48:45.882 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6bc661ec
09:48:45.982 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:48:45.983 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:48:45.985 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:48:46.834 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:48:47.159 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:48:47.171 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 20.767 seconds (JVM running for 21.251)
09:48:47.174 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:48:47.174 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:48:47.174 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:48:47.174 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:48:47.176 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:48:47.177 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:48:47.177 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:48:47.177 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:48:47.177 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:48:47.177 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:50:04.265 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:50:08.111 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
09:50:19.336 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
09:50:19.364 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
09:50:19.385 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
09:50:19.403 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
09:50:19.425 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
09:50:19.445 [http-nio-8081-exec-5] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
09:50:37.760 [http-nio-8081-exec-8] INFO  c.l.c.ThirdPartyController - [receiveThirdPartyData,47] - 接收第三方数据请求，合作方: daihou001, 来源IP: 127.0.0.1
09:50:37.763 [http-nio-8081-exec-8] INFO  c.l.c.ThirdPartyController - [receiveThirdPartyData,120] - 数据解密成功，合作方: daihou001
09:50:37.765 [http-nio-8081-exec-8] INFO  c.l.s.i.ThirdPartyServiceImpl - [processThirdPartyData,50] - 开始处理第三方数据，合作方: daihou001, 请求ID: daihou001_1753926637763_d8e1d676
09:50:37.766 [http-nio-8081-exec-8] INFO  c.l.s.i.ThirdPartyServiceImpl - [processThirdPartyData,55] - 接收到车辆信息 - 车架号: 1HGBH41JXMN109186, 状态: 1
09:50:37.766 [http-nio-8081-exec-8] INFO  c.l.s.i.ThirdPartyServiceImpl - [processDaihou001Data,103] - 处理贷后合作方001数据，解密后的数据: >HLiZM=<BDY(=D?bW4Q#.n_:i4Uy;ba
09:50:37.808 [http-nio-8081-exec-8] INFO  c.l.s.i.ThirdPartyServiceImpl - [processThirdPartyData,83] - 第三方数据处理成功，合作方: daihou001, 请求ID: daihou001_1753926637763_d8e1d676
09:50:37.808 [http-nio-8081-exec-8] INFO  c.l.c.ThirdPartyController - [receiveThirdPartyData,125] - 第三方数据处理成功，合作方: daihou001
09:51:25.498 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23024 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:51:25.502 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:51:25.502 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:51:28.067 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:51:28.070 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:51:28.070 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:51:28.155 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:51:40.920 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:51:41.630 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:51:44.648 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:51:44.655 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:51:44.656 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:51:44.657 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:51:44.657 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:51:44.658 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:51:44.658 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:51:44.658 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@27326f8b
09:51:44.753 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:51:44.753 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:51:44.757 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:51:45.672 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:51:46.010 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:51:46.022 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 20.861 seconds (JVM running for 21.368)
09:51:46.025 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:51:46.025 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:51:46.025 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:51:46.026 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:51:46.027 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:51:46.027 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:51:46.027 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:51:46.027 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:51:46.028 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:51:46.028 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:51:50.435 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:51:50.499 [http-nio-8081-exec-1] INFO  c.l.c.ThirdPartyController - [receiveThirdPartyData,47] - 接收第三方数据请求，合作方: daihou001, 来源IP: 127.0.0.1
09:52:32.387 [http-nio-8081-exec-5] INFO  c.l.c.ThirdPartyController - [receiveThirdPartyData,47] - 接收第三方数据请求，合作方: daihou001, 来源IP: 127.0.0.1
09:54:03.032 [http-nio-8081-exec-10] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
09:54:03.057 [http-nio-8081-exec-10] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
09:54:03.076 [http-nio-8081-exec-10] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
09:55:47.510 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:55:47.508 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 12488 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:55:47.512 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:55:50.263 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:55:50.266 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:55:50.267 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:55:50.355 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:56:03.215 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:56:03.911 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:56:06.917 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:56:06.927 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:56:06.928 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:56:06.928 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:56:06.929 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:56:06.929 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:56:06.930 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:56:06.930 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6bc661ec
09:56:07.034 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:56:07.035 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:56:07.038 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:56:07.863 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:56:08.196 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:56:08.206 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.059 seconds (JVM running for 21.532)
09:56:08.208 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:56:08.209 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:56:08.209 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:56:08.209 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:56:08.210 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:56:08.210 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:56:08.210 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:56:08.210 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:56:08.210 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:56:08.210 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:56:11.970 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:59:10.799 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 17392 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:59:10.803 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:59:10.808 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:59:13.369 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:59:13.372 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:59:13.372 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:59:13.460 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:59:26.332 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:59:27.110 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:59:30.045 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:59:30.053 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:59:30.054 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:59:30.055 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:59:30.056 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:59:30.056 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:59:30.056 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:59:30.056 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@ca2d292
09:59:30.147 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:59:30.148 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:59:30.151 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:59:30.975 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:59:31.294 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:59:31.303 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 20.861 seconds (JVM running for 21.33)
09:59:31.307 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:59:31.307 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:59:31.307 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:59:31.307 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:59:31.308 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:59:31.309 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:59:31.309 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:59:31.309 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:59:31.309 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:59:31.310 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:59:37.287 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:01:56.024 [http-nio-8081-exec-3] INFO  c.r.w.c.l.ThirdPartyController - [receiveThirdPartyData,47] - 接收第三方数据请求，合作方: daihou001, 来源IP: 127.0.0.1
10:03:28.529 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22684 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:03:28.531 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:03:28.532 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:03:31.227 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:03:31.230 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:03:31.230 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:03:31.312 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:03:44.162 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:03:44.921 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:03:47.943 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:03:47.952 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:03:47.952 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:03:47.953 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:03:47.954 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:03:47.954 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:03:47.954 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:03:47.954 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@62318ba6
10:03:48.041 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:03:48.041 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:03:48.044 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:03:48.868 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:03:49.201 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:03:49.211 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.054 seconds (JVM running for 21.603)
10:03:49.214 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:03:49.214 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:03:49.214 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:03:49.214 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:03:49.215 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:03:49.215 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:03:49.215 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:03:49.216 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:03:49.216 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:03:49.216 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:03:52.197 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:03:52.263 [http-nio-8081-exec-1] INFO  c.r.w.c.l.ThirdPartyController - [receiveThirdPartyData,47] - 接收第三方数据请求，合作方: daihou001, 来源IP: 127.0.0.1
10:04:07.484 [http-nio-8081-exec-2] INFO  c.r.w.c.l.ThirdPartyController - [receiveThirdPartyData,47] - 接收第三方数据请求，合作方: daihou001, 来源IP: 127.0.0.1
10:04:07.487 [http-nio-8081-exec-2] INFO  c.r.w.c.l.ThirdPartyController - [receiveThirdPartyData,120] - 数据解密成功，合作方: daihou001
10:04:07.488 [http-nio-8081-exec-2] INFO  c.l.s.i.ThirdPartyServiceImpl - [processThirdPartyData,50] - 开始处理第三方数据，合作方: daihou001, 请求ID: daihou001_1753927447487_ccae68ae
10:04:07.489 [http-nio-8081-exec-2] INFO  c.l.s.i.ThirdPartyServiceImpl - [processThirdPartyData,55] - 接收到车辆信息 - 车架号: 1HGBH41JXMN109186, 状态: 1
10:04:07.489 [http-nio-8081-exec-2] INFO  c.l.s.i.ThirdPartyServiceImpl - [processDaihou001Data,103] - 处理贷后合作方001数据，解密后的数据: >HLiZM=<BDY(=D?bW4Q#.n_:i4Uy;ba
10:04:07.608 [http-nio-8081-exec-2] INFO  c.l.s.i.ThirdPartyServiceImpl - [processThirdPartyData,83] - 第三方数据处理成功，合作方: daihou001, 请求ID: daihou001_1753927447487_ccae68ae
10:04:07.609 [http-nio-8081-exec-2] INFO  c.r.w.c.l.ThirdPartyController - [receiveThirdPartyData,125] - 第三方数据处理成功，合作方: daihou001
10:38:57.491 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
10:39:51.958 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
10:39:51.983 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:39:52.005 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:39:52.025 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:39:52.045 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
10:39:52.065 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:44:48.575 [http-nio-8081-exec-21] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
10:44:48.595 [http-nio-8081-exec-21] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:44:48.617 [http-nio-8081-exec-21] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:44:48.636 [http-nio-8081-exec-21] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:44:48.655 [http-nio-8081-exec-21] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
10:44:48.674 [http-nio-8081-exec-21] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:58:52.785 [http-nio-8081-exec-13] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
10:58:52.804 [http-nio-8081-exec-13] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:58:52.824 [http-nio-8081-exec-13] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:58:52.844 [http-nio-8081-exec-13] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
10:58:52.863 [http-nio-8081-exec-13] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
10:58:52.880 [http-nio-8081-exec-13] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:20:50.587 [http-nio-8081-exec-40] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
11:20:50.618 [http-nio-8081-exec-40] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:20:50.640 [http-nio-8081-exec-40] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:20:50.663 [http-nio-8081-exec-40] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:20:50.690 [http-nio-8081-exec-40] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
11:20:50.717 [http-nio-8081-exec-40] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:29:56.057 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:29:56.057 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25164 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
11:29:56.060 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
11:30:00.903 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
11:30:00.906 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
11:30:00.907 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
11:30:00.992 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
11:30:13.925 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
11:30:14.660 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
11:30:18.152 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:30:18.160 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:30:18.160 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:30:18.161 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:30:18.162 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:30:18.162 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:30:18.162 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:30:18.163 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7eeea18
11:30:18.260 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
11:30:18.260 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
11:30:18.263 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
11:30:19.091 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
11:30:19.428 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:30:19.438 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 23.744 seconds (JVM running for 28.393)
11:30:19.439 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
11:30:19.440 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
11:30:19.440 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
11:30:19.440 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
11:30:19.441 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
11:30:19.441 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
11:30:19.441 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
11:30:19.442 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
11:30:19.442 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
11:30:19.443 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
11:30:52.009 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:30:53.239 [http-nio-8081-exec-3] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
11:30:53.265 [http-nio-8081-exec-3] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:30:53.283 [http-nio-8081-exec-3] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:30:53.304 [http-nio-8081-exec-3] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:30:53.324 [http-nio-8081-exec-3] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
11:30:53.343 [http-nio-8081-exec-3] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:31:56.434 [http-nio-8081-exec-8] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
11:31:56.459 [http-nio-8081-exec-8] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:31:56.492 [http-nio-8081-exec-8] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:31:56.525 [http-nio-8081-exec-8] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:31:56.558 [http-nio-8081-exec-8] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
11:31:56.587 [http-nio-8081-exec-8] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
12:44:47.038 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
12:44:53.454 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
13:14:06.911 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 9484 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:14:06.914 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:14:06.914 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:14:09.417 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:14:09.419 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:14:09.420 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:14:09.511 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:14:22.313 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:14:23.051 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:14:23.685 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
13:14:23.703 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
13:14:23.704 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
13:14:23.705 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
13:14:23.732 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
13:19:22.160 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22300 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:19:22.160 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:19:22.163 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:19:24.622 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:19:24.625 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:19:24.625 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:19:24.717 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:19:37.591 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:19:38.325 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:19:38.916 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
13:19:38.927 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
13:19:38.928 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
13:19:38.930 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
13:19:38.944 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
13:27:01.646 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 24752 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:27:01.651 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:27:01.652 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:27:04.515 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:27:04.518 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:27:04.518 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:27:04.609 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:27:17.515 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:27:18.223 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:27:21.477 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:27:21.487 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:27:21.487 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:27:21.488 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:27:21.489 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:27:21.489 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:27:21.490 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:27:21.490 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1394607f
13:27:21.573 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:27:21.574 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:27:21.578 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:27:22.559 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:27:22.992 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:27:23.004 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.738 seconds (JVM running for 22.295)
13:27:23.008 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:27:23.008 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:27:23.009 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:27:23.009 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:27:23.010 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:27:23.010 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:27:23.010 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:27:23.011 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:27:23.011 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:27:23.011 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:27:41.380 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:33:38.653 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 24256 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:33:38.656 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:33:38.658 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:33:41.318 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:33:41.320 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:33:41.320 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:33:41.401 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:33:43.257 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:33:43.999 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:33:47.028 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:33:47.037 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:33:47.038 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:33:47.038 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:33:47.039 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:33:47.039 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:33:47.040 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:33:47.040 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6b205ea7
13:33:47.120 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:33:47.121 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:33:47.123 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:33:47.972 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:33:48.334 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:33:48.342 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.03 seconds (JVM running for 10.498)
13:33:48.346 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:33:48.347 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:33:48.347 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:33:48.347 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:33:48.348 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:33:48.348 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:33:48.348 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:33:48.349 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:33:48.349 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:33:48.349 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:33:53.037 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:36:57.021 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 9608 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:36:57.023 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:36:57.024 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:36:59.832 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:36:59.835 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:36:59.835 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:36:59.923 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:37:12.942 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:37:13.681 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:37:16.893 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:37:16.901 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:37:16.902 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:37:16.902 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:37:16.903 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:37:16.903 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:37:16.903 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:37:16.903 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@70de55b2
13:37:16.987 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:37:16.988 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:37:16.991 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:37:17.919 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:37:18.267 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:37:18.277 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.651 seconds (JVM running for 22.173)
13:37:18.278 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:37:18.280 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:37:18.280 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:37:18.280 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:37:18.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:37:18.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:37:18.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:37:18.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:37:18.281 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:37:18.282 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:37:21.266 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:52:23.123 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 11452 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:52:23.126 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:52:23.129 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:52:25.769 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:52:25.772 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:52:25.772 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:52:25.851 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:52:38.669 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:52:39.399 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:52:42.390 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:52:42.399 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:52:42.399 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:52:42.400 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:52:42.401 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:52:42.401 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:52:42.401 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:52:42.402 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3a42d
13:52:42.499 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:52:42.499 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:52:42.502 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:52:43.356 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:52:43.707 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:52:43.716 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 20.942 seconds (JVM running for 21.446)
13:52:43.717 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:52:43.718 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:52:43.718 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:52:43.718 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:52:43.719 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:52:43.719 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:52:43.719 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:52:43.719 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:52:43.719 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:52:43.719 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:52:47.944 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:58:30.822 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 1136 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:58:30.825 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:58:30.826 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:58:33.321 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:58:33.324 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:58:33.324 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:58:33.411 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:58:35.209 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:58:35.915 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:58:49.902 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:58:49.914 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:58:49.914 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:58:49.915 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:58:49.916 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:58:49.916 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:58:49.916 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:58:49.916 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@19b2d9e
13:58:50.019 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:58:50.019 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:58:50.022 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:58:50.809 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:58:51.144 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:58:51.152 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 20.669 seconds (JVM running for 21.147)
13:58:51.154 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:58:51.155 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:58:51.155 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:58:51.155 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:58:51.156 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:58:51.157 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:58:51.157 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:58:51.157 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:58:51.157 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:58:51.158 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:58:58.372 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:06:49.114 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25028 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:06:49.117 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:06:49.117 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:06:51.944 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:06:51.947 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:06:51.947 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:06:52.025 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:07:05.009 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:07:05.814 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:07:08.735 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:07:08.746 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:07:08.746 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:07:08.747 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:07:08.748 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:07:08.748 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:07:08.748 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:07:08.748 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@48749dd8
14:07:08.852 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:07:08.853 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:07:08.856 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:07:09.667 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:07:09.995 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:07:10.004 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.273 seconds (JVM running for 21.765)
14:07:10.006 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:07:10.006 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:07:10.006 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:07:10.006 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:07:10.007 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:07:10.007 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:07:10.007 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:07:10.007 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:07:10.007 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:07:10.007 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:07:34.710 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:16:25.071 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 20244 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:16:25.074 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:16:25.077 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:16:27.592 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:16:27.595 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:16:27.596 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:16:27.682 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:16:40.481 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:16:41.229 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:16:44.273 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:16:44.282 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:16:44.283 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:16:44.284 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:16:44.284 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:16:44.285 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:16:44.285 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:16:44.285 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1a9fa5b3
14:16:44.381 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:16:44.382 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:16:44.385 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:16:45.234 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:16:45.584 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:16:45.592 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 20.87 seconds (JVM running for 21.363)
14:16:45.594 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:16:45.595 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:16:45.595 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:16:45.595 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:16:45.596 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:16:45.597 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:16:45.598 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:16:45.599 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:16:45.599 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:16:45.599 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:18:30.533 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:19:46.579 [http-nio-8081-exec-12] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000005
14:35:55.626 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 17012 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:35:55.630 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:35:55.630 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:35:58.232 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:35:58.235 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:35:58.235 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:35:58.325 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:36:00.123 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:36:00.876 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:36:03.933 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:36:03.944 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:36:03.944 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:36:03.946 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:36:03.947 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:36:03.947 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:36:03.947 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:36:03.947 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@ec69c6
14:36:04.032 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:36:04.034 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:36:04.040 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:36:04.899 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:36:05.242 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:36:05.252 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.976 seconds (JVM running for 10.56)
14:36:05.254 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:36:05.255 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:36:05.255 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:36:05.255 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:36:05.256 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:36:05.256 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:36:05.256 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:36:05.256 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:36:05.257 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:36:05.257 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:36:15.199 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:36:18.562 [http-nio-8081-exec-5] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000002
14:36:20.107 [http-nio-8081-exec-6] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000002
14:36:21.609 [http-nio-8081-exec-7] INFO  c.r.v.c.VwCustomerInfoController - [getInfo,75] - 查询vw_customer_info信息：PC0000000003
14:40:38.602 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 14448 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:40:38.605 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:40:38.607 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:40:41.374 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:40:41.377 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:40:41.377 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:40:41.467 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:40:54.211 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:40:54.924 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:40:57.978 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:40:57.987 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:40:57.988 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:40:57.989 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:40:57.990 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:40:57.990 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:40:57.990 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:40:57.990 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@faaa5d
14:40:58.073 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:40:58.074 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:40:58.076 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:40:58.912 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:40:59.255 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:40:59.267 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.024 seconds (JVM running for 21.495)
14:40:59.270 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:40:59.270 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:40:59.270 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:40:59.270 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:40:59.271 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:40:59.271 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:40:59.271 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:40:59.271 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:40:59.271 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:40:59.271 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:41:03.502 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:42:06.109 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 11896 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:42:06.112 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:42:06.113 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:42:08.555 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:42:08.557 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:42:08.558 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:42:08.643 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:42:21.438 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:42:22.145 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:42:25.573 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:42:25.584 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:42:25.584 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:42:25.584 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:42:25.584 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:42:25.584 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:42:25.584 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:42:25.584 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@336956d7
14:42:25.686 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:42:25.687 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:42:25.690 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:42:26.679 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:42:27.075 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:42:27.085 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.325 seconds (JVM running for 21.827)
14:42:27.085 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:42:27.091 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:42:27.091 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:42:27.091 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:42:27.091 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:42:27.091 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:42:27.091 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:42:27.091 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:42:27.091 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:42:27.091 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:42:34.148 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:43:24.895 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:43:24.894 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25672 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:43:24.896 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:43:27.466 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:43:27.468 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:43:27.468 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:43:27.558 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:43:40.380 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:43:41.081 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:43:44.125 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:43:44.133 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:43:44.134 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:43:44.134 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:43:44.135 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:43:44.136 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:43:44.136 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:43:44.136 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@e5e4f1f
14:43:44.232 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:43:44.233 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:43:44.236 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:43:45.068 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:43:45.449 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:43:45.461 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 20.91 seconds (JVM running for 21.428)
14:43:45.463 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:43:45.464 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:43:45.464 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:43:45.464 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:43:45.465 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:43:45.466 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:43:45.466 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:43:45.466 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:43:45.467 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:43:45.467 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:43:55.528 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25608 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
14:43:55.532 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
14:43:55.534 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:43:58.002 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
14:43:58.005 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
14:43:58.006 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
14:43:58.092 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
14:43:59.889 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
14:44:00.603 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
14:44:03.618 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:44:03.629 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:44:03.630 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:44:03.631 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
14:44:03.632 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

14:44:03.632 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
14:44:03.632 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:44:03.633 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6058d5d
14:44:03.710 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
14:44:03.711 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
14:44:03.714 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
14:44:04.608 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
14:44:04.970 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
14:44:04.981 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.786 seconds (JVM running for 10.273)
14:44:04.984 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
14:44:04.984 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
14:44:04.984 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
14:44:04.985 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
14:44:04.986 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
14:44:04.986 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
14:44:04.986 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
14:44:04.986 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
14:44:04.986 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
14:44:04.987 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
14:45:23.393 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:00:45.170 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 12740 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
15:00:45.173 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:00:45.173 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:00:47.627 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:00:47.629 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:00:47.630 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:00:47.712 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:01:00.425 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:01:01.517 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:01:04.527 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:01:04.538 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:01:04.538 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:01:04.539 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:01:04.539 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:01:04.540 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:01:04.540 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:01:04.540 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4fa01f38
15:01:04.629 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
15:01:04.629 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
15:01:04.633 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
15:01:05.426 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:01:05.786 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:01:05.795 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 20.975 seconds (JVM running for 21.507)
15:01:05.797 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
15:01:05.797 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
15:01:05.797 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
15:01:05.798 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
15:01:05.799 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
15:01:05.799 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
15:01:05.799 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
15:01:05.799 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
15:01:05.799 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
15:01:05.800 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
15:01:25.973 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:18:15.081 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
15:18:15.108 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
15:18:15.128 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
15:18:15.148 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
15:18:15.169 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
15:18:15.188 [http-nio-8081-exec-15] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
15:41:38.903 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25396 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
15:41:38.905 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:41:38.906 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
15:41:41.442 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
15:41:41.446 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
15:41:41.446 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
15:41:41.536 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
15:41:54.577 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
15:41:55.494 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
15:41:58.430 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:41:58.439 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:41:58.439 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:41:58.440 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
15:41:58.440 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

15:41:58.441 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
15:41:58.441 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:41:58.441 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@341678a6
15:41:58.522 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
15:41:58.523 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
15:41:58.525 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
15:41:59.313 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
15:41:59.653 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
15:41:59.665 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 21.12 seconds (JVM running for 21.627)
15:41:59.668 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
15:41:59.668 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
15:41:59.668 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
15:41:59.668 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
15:41:59.669 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
15:41:59.669 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
15:41:59.670 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
15:41:59.671 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
15:41:59.671 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
15:41:59.671 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
15:46:02.127 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:46:03.367 [http-nio-8081-exec-4] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
15:46:03.390 [http-nio-8081-exec-4] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
15:46:03.411 [http-nio-8081-exec-4] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
15:46:03.432 [http-nio-8081-exec-4] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
15:46:03.452 [http-nio-8081-exec-4] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
15:46:03.470 [http-nio-8081-exec-4] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
16:14:35.914 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 27776 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
16:14:35.914 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:14:35.918 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
16:14:38.527 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
16:14:38.530 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
16:14:38.530 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
16:14:38.620 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
