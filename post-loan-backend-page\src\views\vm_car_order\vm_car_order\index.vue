<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="" prop="customerName">
        <el-input v-model="queryParams.customerName" placeholder="贷款人账户、姓名" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="plateNo">
        <el-input v-model="queryParams.plateNo" placeholder="请输入车牌号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="jgName">
        <el-input v-model="queryParams.jgName" placeholder="录单渠道名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="garageName">
        <el-input v-model="queryParams.garageName" placeholder="车库名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="keyStatus">
        <el-select v-model="queryParams.keyStatus" placeholder="请选择钥匙状态" clearable>
          <el-option v-for="dict in keyStatusList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable>
          <el-option v-for="dict in statusList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="teamName">
        <el-select v-model="queryParams.teamName" placeholder="请选择找车团队" clearable>
          <el-option v-for="item in teamList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="派单时间">
        <el-date-picker v-model="queryParams.originallyTime" style="width: 240px" value-format="yyyy-MM-dd"
          type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['vm_car_order:vm_car_order:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vm_car_order:vm_car_order:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vm_car_order:vm_car_order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['vm_car_order:vm_car_order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="vm_car_orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="55" fixed="left" />
      <el-table-column label="贷款人" align="center" prop="customerName">
        <template slot-scope="scope">
          <el-button type="text"
            @click="openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyNo })">
            {{ scope.row.customerName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="mobilePhone" width="130" />
      <el-table-column label="出单渠道" align="center" prop="jgName" width="130" />
      <el-table-column label="逾期状态" align="center" prop="slippageStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.slippageStatus != null">
            {{
              scope.row.slippageStatus == 1
                ? '提醒'
                : scope.row.slippageStatus == 2
                  ? '电催'
                  : scope.row.slippageStatus == 3
                    ? '上访'
                    : scope.row.slippageStatus == 4
                      ? '逾期30-60'
                      : '逾期60+'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="欠款金额" align="center" prop="overdueAmt" width="130" />
      <el-table-column label="派单员" align="center" prop="dispatcher" width="130">
        <template slot-scope="scope">
          <span>
            {{ scope.row.dispatcher === 1 ? '贷后文员' : scope.row.dispatcher === 2 ? '法诉文员' : '' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="业务员" align="center" prop="nickName" />
      <el-table-column label="车牌号" align="center" prop="plateNo">
        <template slot-scope="scope">
          <el-button type="text" @click="openCarInfo(scope.row.plateNo)">{{ scope.row.plateNo }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="车辆位置" align="center" prop="carDetailAddress" />
      <el-table-column label="车辆状态" align="center" prop="carStatus">
        <template slot-scope="scope">
          <span>
            {{
              scope.row.carStatus == 1
                ? '省内正常行驶'
                : scope.row.carStatus == 2
                  ? '省外正常行驶'
                  : scope.row.carStatus == 3
                    ? '抵押'
                    : scope.row.carStatus == 4
                      ? '疑似抵押'
                      : scope.row.carStatus == 5
                        ? '疑似黑车'
                        : scope.row.carStatus == 6
                          ? '已入库'
                          : scope.row.carStatus == 7
                            ? '车在法院'
                            : scope.row.carStatus == 8
                              ? '已法拍'
                              : '协商卖车'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="GPS状态" align="center" prop="gpsStatus">
        <template slot-scope="scope">
          <span>
            {{
              scope.row.gpsStatus == 1 ? '部分拆除' : scope.row.gpsStatus == 2 ? '全部拆除' : scope.row.gpsStatus == 3 ? 'GPS正常' :
                '停车30天以上'
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="车库名称" align="center" prop="garageName" width="130" />
      <el-table-column label="找车团队" align="center" prop="teamName" />
      <el-table-column label="派单时间" align="center" prop="allocationTime" width="180" />
      <el-table-column label="钥匙状态" align="center" prop="keyStatus">
        <template slot-scope="scope">
          <span>{{ scope.row.keyStatus == 1 ? '已邮寄' : scope.row.keyStatus == 2 ? '已收回' : '未归还' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="status">
        <template slot-scope="scope">
          <span>{{ statusList[scope.row.status].label || '暂无数据' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="钥匙邮寄时间" align="center" prop="keyTime" width="180"></el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="200">
        <template slot-scope="scope">
          <el-button style="margin-left: 10px" size="mini" type="text" @click="handleUpdate(scope.row)"
            v-hasPermi="['vm_car_order:vm_car_order:edit']">
            邮寄钥匙
          </el-button>
          <el-button v-if="scope.row.status !== 4" size="mini" type="text"
            @click="handleRevoke(scope.row)">撤销订单</el-button>
          <el-button v-if="scope.row.status === 2" size="mini" type="text"
            @click="handleSubmitCost(scope.row)">提交找车费用</el-button>
          <!-- v-hasPermi="['vm_car_order:vm_car_order:remove']" -->
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 邮寄钥匙对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="邮寄地址">
          <el-select @change="provinceChange" v-model="form.keyProvince" value-key="children" placeholder="请选择省">
            <el-option v-for="dict in provinceList" :key="dict.name" :label="dict.name" :value="dict" />
          </el-select>
          <el-select @change="cityChange" v-model="form.keyCity" value-key="children" placeholder="请选择市"
            style="margin-top: 10px">
            <el-option v-for="dict in cityList" :key="dict.name" :label="dict.name" :value="dict" />
          </el-select>
          <el-select @change="districtChange" v-model="form.keyBorough" value-key="children" placeholder="请选择区"
            style="margin-top: 10px">
            <el-option v-for="dict in districtList" :key="dict.name" :label="dict.name" :value="dict" />
          </el-select>
        </el-form-item>
        <el-form-item label="详细地址">
          <el-input v-model="form.keyAddress" placeholder="请填写详细地址" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 提交找车费用对话框 -->
    <el-dialog title="提交找车费用" :visible.sync="costDialogVisible" width="600px" append-to-body>
      <el-form ref="costForm" :model="costForm" :rules="costRules" label-width="120px">
        <el-form-item label="佣金" prop="transportationFee">
          <el-input-number
            v-model="costForm.transportationFee"
            :precision="2"
            :min="0"
            :max="999999"
            placeholder="请输入佣金"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
        <el-form-item label="拖车费" prop="towingFee">
          <el-input-number
            v-model="costForm.towingFee"
            :precision="2"
            :min="0"
            :max="999999"
            placeholder="请输入拖车费"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
        <el-form-item label="贴机费" prop="trackerInstallationFee">
          <el-input-number
            v-model="costForm.trackerInstallationFee"
            :precision="2"
            :min="0"
            :max="999999"
            placeholder="请输入贴机费"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
        <el-form-item label="其他报销" prop="otherReimbursement">
          <el-input-number
            v-model="costForm.otherReimbursement"
            :precision="2"
            :min="0"
            :max="999999"
            placeholder="请输入其他报销费用"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
        <el-form-item label="总费用">
          <el-input :value="totalCost" readonly style="width: 100%">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCostForm">确 定</el-button>
        <el-button @click="cancelCost">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 贷款人信息组件 -->
    <userInfo ref="userInfo" :visible.sync="userInfoVisible" title="贷款人信息" :customerInfo="customerInfo" />
    <!-- 车辆信息组件 -->
    <carInfo ref="carInfo" :visible.sync="carInfoVisible" title="车辆信息" :plateNo="plateNo" permission="2" />
  </div>
</template>

<script>
import {
  listVm_car_order,
  teamVm_car_order,
  getVm_car_order,
  delVm_car_order,
  addVm_car_order,
  updateVm_car_order,
  RevokeVm_car_order,
  submitCarFindCost,
  mailKey,
} from '@/api/vm_car_order/vm_car_order'
import { listCar_team } from '@/api/car_team/car_team'
import areaList from '../../../assets/area.json'
import userInfo from '@/layout/components/Dialog/userInfo.vue'
import carInfo from '@/layout/components/Dialog/carInfo.vue'
export default {
  name: 'Vm_car_order',
  components: {
    userInfo,
    carInfo,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // VIEW表格数据
      vm_car_orderList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 15,
        customerName: '',
        plateNo: '',
        jgName: '',
        garageName: '',
        keyStatus: '',
        status: '',
        teamName: '',
        startTime: '',
        endTime: '',
        originallyTime: '',
      },
      jgNameList: [
        { label: 'A公司', value: 1 },
        { label: 'B公司', value: 2 },
      ],
      keyStatusList: [
        { label: '已邮寄', value: 1 },
        { label: '已收回', value: 2 },
        { label: '已归还', value: 3 },
      ],
      statusList: [
        { label: '发起订单', value: 0 },
        { label: '已分配', value: 1 },
        { label: '已完成', value: 2 },
        { label: '未完成', value: 3 },
        { label: '已撤销', value: 4 },
      ],
      teamList: [],
      // 表单参数
      form: {
        id: '',
        keyProvince: '',
        keyCity: '',
        keyBorough: '',
        keyAddress: '',
      },
      // 表单校验
      rules: {
        keyProvince: '',
        keyCity: '',
        keyBorough: '',
        keyAddress: '',
      },
      provinceList: areaList,
      cityList: [],
      districtList: [],
      revokeList: {
        id: '',
        status: 4,
      },
      customerInfo: { customerId: '', applyId: '' },
      userInfoVisible: false,
      plateNo: '',
      carInfoVisible: false,
      // 找车费用相关
      costDialogVisible: false,
      costForm: {
        id: '',
        applyNo: '',
        loanId: '',
        transportationFee: 0,
        towingFee: 0,
        trackerInstallationFee: 0,
        otherReimbursement: 0
      },
      costRules: {
        transportationFee: [
          { type: 'number', min: 0, message: '佣金不能小于0', trigger: 'blur' }
        ],
        towingFee: [
          { type: 'number', min: 0, message: '拖车费不能小于0', trigger: 'blur' }
        ],
        trackerInstallationFee: [
          { type: 'number', min: 0, message: '贴机费不能小于0', trigger: 'blur' }
        ],
        otherReimbursement: [
          { type: 'number', min: 0, message: '其他报销不能小于0', trigger: 'blur' }
        ]
      },
    }
  },
  computed: {
    // 计算总费用
    totalCost() {
      const transportationFee = this.costForm.transportationFee || 0
      const towingFee = this.costForm.towingFee || 0
      const trackerInstallationFee = this.costForm.trackerInstallationFee || 0
      const otherReimbursement = this.costForm.otherReimbursement || 0
      return (transportationFee + towingFee + trackerInstallationFee + otherReimbursement).toFixed(2)
    }
  },
  created() {
    this.getTeamList()
    this.getList()
  },
  methods: {
    handleChange(value) {
      this.queryParams.jgName = value
    },
    provinceChange(item) {
      this.form.keyProvince = item.name
      this.cityList = item.children
    },
    cityChange(item) {
      this.form.keyCity = item.name
      this.districtList = item.children
    },
    districtChange(item) {
      this.form.keyBorough = item.name
    },
    async getTeamList() {
      try {
        const res = await listCar_team()
        this.teamList = (res.rows || []).map(item => ({
          label: item.teamName,
          value: item.teamName
        }))
      } catch (e) {
        this.teamList = []
      }
    },
    /** 查询VIEW列表 */
    getList() {
      this.loading = true
      listVm_car_order(this.queryParams).then(response => {
        this.vm_car_orderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        keyProvince: '',
        keyCity: '',
        keyBorough: '',
        keyAddress: '',
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.originallyTime) {
        this.queryParams.startTime = this.queryParams.originallyTime[0]
        this.queryParams.endTime = this.queryParams.originallyTime[1]
      }
      // delete this.queryParams.originallyTime
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.customerName = null
      this.queryParams.plateNo = null
      this.queryParams.jgName = null
      this.queryParams.garageName = null
      this.queryParams.keyStatus = null
      this.queryParams.status = null
      this.queryParams.collectionMethod = null
      this.queryParams.teamName = null
      this.queryParams.originallyTime = null
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.handleQuery()
    },
    restSearch() {
      this.queryParams = {
        customerName: '',
        plateNo: '',
        jgName: '',
        garageId: '',
        keyStatus: '',
        status: '',
        teamName: '',
        startTime: '',
        endTime: '',
        originallyTime: '',
        pageNum: 1,
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加VIEW'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form.id = row.id
      this.form.keyProvince = row.keyProvince
      this.form.keyCity = row.keyCity
      this.form.keyBorough = row.keyBorough
      this.form.keyAddress = row.keyAddress
      const id = row.id || this.ids
      getVm_car_order(id).then(response => {
        // this.form = response.data
        this.open = true
        this.title = '邮寄钥匙'
      })
    },
    /** 提交按钮 */
    submitForm() {
      // 邮寄钥匙逻辑
      mailKey(this.form).then(() => {
        this.$modal.msgSuccess('钥匙邮寄成功')
        this.open = false
        this.getList()
      }).catch(error => {
        this.$modal.msgError('邮寄失败：' + (error.message || '未知错误'))
      })
      // this.$refs["form"].validate(valid => {
      //   if (valid) {
      //     if (this.form.id != null) {
      //       updateVm_car_order(this.form).then(response => {
      //         this.$modal.msgSuccess("修改成功")
      //         this.open = false
      //         this.getList()
      //       })
      //     } else {
      //       addVm_car_order(this.form).then(response => {
      //         this.$modal.msgSuccess("新增成功")
      //         this.open = false
      //         this.getList()
      //       })
      //     }
      //   }
      // })
    },
    handleRevoke(row) {
      console.log('1111')
      this.revokeList.id = row.id
      var data = {
        id: row.id,
        status: 4,
      }
      // const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认撤销？')
        .then(() => {
          return RevokeVm_car_order(data)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('撤销成功')
        })
        .catch(err => {
          console.log(err)
        })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认撤销编号为"' + ids + '"的数据项？')
        .then(function () {
          return delVm_car_order(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('撤销成功')
        })
        .catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'vm_car_order/vm_car_order/export',
        {
          ...this.queryParams,
        },
        `vm_car_order_${new Date().getTime()}.xlsx`
      )
    },
    openUserInfo(customerInfo) {
      this.customerInfo = customerInfo
      this.userInfoVisible = true
    },
    openCarInfo(plateNo) {
      this.plateNo = plateNo
      this.carInfoVisible = true
    },
    // 处理提交找车费用
    handleSubmitCost(row) {
      this.resetCostForm()
      this.costForm.id = row.id
      this.costForm.applyNo = row.applyNo
      this.costForm.loanId = row.loanId
      this.costDialogVisible = true
    },
    // 重置找车费用表单
    resetCostForm() {
      this.costForm = {
        id: '',
        applyNo: '',
        loanId: '',
        transportationFee: 0,
        towingFee: 0,
        trackerInstallationFee: 0,
        otherReimbursement: 0
      }
      if (this.$refs.costForm) {
        this.$refs.costForm.resetFields()
      }
    },
    // 取消找车费用提交
    cancelCost() {
      this.costDialogVisible = false
      this.resetCostForm()
    },
    // 提交找车费用表单
    submitCostForm() {
      this.$refs.costForm.validate(valid => {
        if (valid) {
          // 调用API提交找车费用
          submitCarFindCost(this.costForm).then(() => {
            this.$modal.msgSuccess('找车费用提交成功')
            this.costDialogVisible = false
            this.resetCostForm()
            this.getList()
          })
        }
      })
    },
  },
}
</script>
