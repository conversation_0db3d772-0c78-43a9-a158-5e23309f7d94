package com.ruoyi.car_order_examine.service.impl;

import java.util.List;
import java.util.ArrayList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.car_order_examine.mapper.CarOrderExamineMapper;
import com.ruoyi.car_order_examine.domain.CarOrderExamine;
import com.ruoyi.car_order_examine.service.ICarOrderExamineService;

/**
 * 找车费用审批Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class CarOrderExamineServiceImpl implements ICarOrderExamineService 
{
    @Autowired
    private CarOrderExamineMapper carOrderExamineMapper;

    /**
     * 查询找车费用审批
     *
     * @param id 找车费用审批主键
     * @return 找车费用审批
     */
    @Override
    public CarOrderExamine selectCarOrderExamineById(String id)
    {
        return carOrderExamineMapper.selectCarOrderExamineById(id);
    }

    /**
     * 根据申请编号查询找车费用审批
     *
     * @param applyNo 申请编号
     * @return 找车费用审批
     */
    @Override
    public CarOrderExamine selectCarOrderExamineByApplyNo(String applyNo)
    {
        return carOrderExamineMapper.selectCarOrderExamineByApplyNo(applyNo);
    }

    /**
     * 根据贷款ID查询找车费用审批
     *
     * @param loanId 贷款ID
     * @return 找车费用审批
     */
    @Override
    public CarOrderExamine selectCarOrderExamineByLoanId(String loanId)
    {
        return carOrderExamineMapper.selectCarOrderExamineByLoanId(loanId);
    }

    /**
     * 查询找车费用审批列表
     * 
     * @param carOrderExamine 找车费用审批
     * @return 找车费用审批
     */
    @Override
    public List<CarOrderExamine> selectCarOrderExamineList(CarOrderExamine carOrderExamine)
    {
        return carOrderExamineMapper.selectCarOrderExamineList(carOrderExamine);
    }

    /**
     * 新增找车费用审批
     * 
     * @param carOrderExamine 找车费用审批
     * @return 结果
     */
    @Override
    public int insertCarOrderExamine(CarOrderExamine carOrderExamine)
    {
        return carOrderExamineMapper.insertCarOrderExamine(carOrderExamine);
    }

    /**
     * 修改找车费用审批
     * 
     * @param carOrderExamine 找车费用审批
     * @return 结果
     */
    @Override
    public int updateCarOrderExamine(CarOrderExamine carOrderExamine)
    {
        return carOrderExamineMapper.updateCarOrderExamine(carOrderExamine);
    }

    /**
     * 批量删除找车费用审批
     * 
     * @param ids 需要删除的找车费用审批主键
     * @return 结果
     */
    @Override
    public int deleteCarOrderExamineByIds(String[] ids)
    {
        return carOrderExamineMapper.deleteCarOrderExamineByIds(ids);
    }

    /**
     * 删除找车费用审批信息
     *
     * @param id 找车费用审批主键
     * @return 结果
     */
    @Override
    public int deleteCarOrderExamineById(String id)
    {
        return carOrderExamineMapper.deleteCarOrderExamineById(id);
    }

    /**
     * 根据用户角色查询待审批列表
     *
     * @param userRole 用户角色
     * @return 待审批列表
     */
    @Override
    public List<CarOrderExamine> selectPendingApprovalList(String userRole)
    {
        CarOrderExamine query = new CarOrderExamine();

        // 根据用户角色设置查询条件
        if ("法诉主管".equals(userRole)) {
            query.setStatus(CarOrderExamine.STATUS_PENDING);
        } else if ("总监".equals(userRole)) {
            // 总监可以审批法诉主管审批后的和总监审批阶段的
            query.setStatus(CarOrderExamine.STATUS_LEGAL_SUPERVISOR);
            List<CarOrderExamine> list1 = carOrderExamineMapper.selectCarOrderExamineList(query);
            query.setStatus(CarOrderExamine.STATUS_DIRECTOR);
            List<CarOrderExamine> list2 = carOrderExamineMapper.selectCarOrderExamineList(query);

            List<CarOrderExamine> result = new ArrayList<>();
            result.addAll(list1);
            result.addAll(list2);
            return result;
        } else if ("总经理".equals(userRole) || "董事长".equals(userRole)) {
            query.setStatus(CarOrderExamine.STATUS_DIRECTOR_CC);
        } else {
            // 其他角色返回空列表
            return new ArrayList<>();
        }

        return carOrderExamineMapper.selectCarOrderExamineList(query);
    }
}
