package com.ruoyi.daily_expense_approval.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 日常花费审批对象 daily_expense_approval
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class DailyExpenseApproval extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    // 审批状态常量
    /** 待审批 */
    public static final String STATUS_PENDING = "0";
    /** 全部通过 */
    public static final String STATUS_APPROVED = "1";
    /** 已拒绝 */
    public static final String STATUS_REJECTED = "2";
    /** 主管审批 */
    public static final String STATUS_SUPERVISOR = "3";
    /** 总监审批 */
    public static final String STATUS_DIRECTOR = "4";
    /** 财务主管审批 */
    public static final String STATUS_FINANCE_SUPERVISOR = "5";
    /** 总经理审批 */
    public static final String STATUS_GENERAL_MANAGER = "6";

    /** 主键ID */
    private Long id;

    /** 关联法诉案件ID */
    @Excel(name = "关联法诉案件ID")
    private Long litigationCaseId;

    /** 费用类型（油费、路费、餐费、住宿费、交通费、其他） */
    @Excel(name = "费用类型", readConverterExp = "油=费、路费、餐费、住宿费、交通费、其他")
    private String expenseType;

    /** 费用金额 */
    @Excel(name = "费用金额")
    private BigDecimal expenseAmount;

    /** 费用发生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "费用发生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expenseDate;

    /** 费用说明 */
    @Excel(name = "费用说明")
    private String expenseDescription;

    /** 发票/凭证图片地址 */
    @Excel(name = "发票/凭证图片地址")
    private String receiptUrl;

    /** 申请人ID */
    @Excel(name = "申请人ID")
    private String applicantId;

    /** 申请人姓名 */
    @Excel(name = "申请人姓名")
    private String applicantName;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applicationTime;

    /** 审批状态（0-待审批，1-全部通过，2-已拒绝 3-主管审批 4-总监审批  5-财务主管审批  6-总经理审批） */
    @Excel(name = "审批状态", readConverterExp = "审批状态（0-待审批，1-全部通过，2-已拒绝 3-主管审批 4-总监审批  5-财务主管审批  6-总经理审批）")
    private String approvalStatus;

    /** 审批人ID */
    @Excel(name = "审批人ID")
    private String approverId;

    /** 审批人姓名 */
    @Excel(name = "审批人姓名")
    private String approverName;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date approvalTime;

    /** 审批备注 */
    @Excel(name = "审批备注")
    private String approvalRemark;

    // 查询条件字段
    /** 审批开始时间 */
    private String approvalStartTime;

    /** 审批结束时间 */
    private String approvalEndTime;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setLitigationCaseId(Long litigationCaseId) 
    {
        this.litigationCaseId = litigationCaseId;
    }

    public Long getLitigationCaseId() 
    {
        return litigationCaseId;
    }

    public void setExpenseType(String expenseType) 
    {
        this.expenseType = expenseType;
    }

    public String getExpenseType() 
    {
        return expenseType;
    }

    public void setExpenseAmount(BigDecimal expenseAmount) 
    {
        this.expenseAmount = expenseAmount;
    }

    public BigDecimal getExpenseAmount() 
    {
        return expenseAmount;
    }

    public void setExpenseDate(Date expenseDate) 
    {
        this.expenseDate = expenseDate;
    }

    public Date getExpenseDate() 
    {
        return expenseDate;
    }

    public void setExpenseDescription(String expenseDescription) 
    {
        this.expenseDescription = expenseDescription;
    }

    public String getExpenseDescription() 
    {
        return expenseDescription;
    }

    public void setReceiptUrl(String receiptUrl) 
    {
        this.receiptUrl = receiptUrl;
    }

    public String getReceiptUrl() 
    {
        return receiptUrl;
    }

    public void setApplicantId(String applicantId) 
    {
        this.applicantId = applicantId;
    }

    public String getApplicantId() 
    {
        return applicantId;
    }

    public void setApplicantName(String applicantName) 
    {
        this.applicantName = applicantName;
    }

    public String getApplicantName() 
    {
        return applicantName;
    }

    public void setApplicationTime(Date applicationTime) 
    {
        this.applicationTime = applicationTime;
    }

    public Date getApplicationTime() 
    {
        return applicationTime;
    }

    public void setApprovalStatus(String approvalStatus) 
    {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() 
    {
        return approvalStatus;
    }

    public void setApproverId(String approverId) 
    {
        this.approverId = approverId;
    }

    public String getApproverId() 
    {
        return approverId;
    }

    public void setApproverName(String approverName) 
    {
        this.approverName = approverName;
    }

    public String getApproverName() 
    {
        return approverName;
    }

    public void setApprovalTime(Date approvalTime) 
    {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() 
    {
        return approvalTime;
    }

    public void setApprovalRemark(String approvalRemark) 
    {
        this.approvalRemark = approvalRemark;
    }

    public String getApprovalRemark() 
    {
        return approvalRemark;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("litigationCaseId", getLitigationCaseId())
            .append("expenseType", getExpenseType())
            .append("expenseAmount", getExpenseAmount())
            .append("expenseDate", getExpenseDate())
            .append("expenseDescription", getExpenseDescription())
            .append("receiptUrl", getReceiptUrl())
            .append("applicantId", getApplicantId())
            .append("applicantName", getApplicantName())
            .append("applicationTime", getApplicationTime())
            .append("approvalStatus", getApprovalStatus())
            .append("approverId", getApproverId())
            .append("approverName", getApproverName())
            .append("approvalTime", getApprovalTime())
            .append("approvalRemark", getApprovalRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .toString();
    }

    public String getApprovalStartTime() {
        return approvalStartTime;
    }

    public void setApprovalStartTime(String approvalStartTime) {
        this.approvalStartTime = approvalStartTime;
    }

    public String getApprovalEndTime() {
        return approvalEndTime;
    }

    public void setApprovalEndTime(String approvalEndTime) {
        this.approvalEndTime = approvalEndTime;
    }

    /**
     * 获取下一个审批状态
     * @return 下一个审批状态
     */
    public String getNextApprovalStatus() {
        if (STATUS_PENDING.equals(this.approvalStatus)) {
            return STATUS_SUPERVISOR; // 0 -> 3 主管审批
        } else if (STATUS_SUPERVISOR.equals(this.approvalStatus)) {
            return STATUS_DIRECTOR; // 3 -> 4 总监审批
        } else if (STATUS_DIRECTOR.equals(this.approvalStatus)) {
            return STATUS_FINANCE_SUPERVISOR; // 4 -> 5 财务主管审批
        } else if (STATUS_FINANCE_SUPERVISOR.equals(this.approvalStatus)) {
            return STATUS_GENERAL_MANAGER; // 5 -> 6 总经理审批
        } else if (STATUS_GENERAL_MANAGER.equals(this.approvalStatus)) {
            return STATUS_APPROVED; // 6 -> 1 全部通过
        }
        return this.approvalStatus; // 其他状态保持不变
    }

    /**
     * 判断指定角色是否可以审批当前状态
     * @param userRole 用户角色
     * @return 是否可以审批
     */
    public boolean canApprove(String userRole) {
        if (STATUS_APPROVED.equals(this.approvalStatus) || STATUS_REJECTED.equals(this.approvalStatus)) {
            return false; // 已完成的不能再审批
        }

        switch (this.approvalStatus) {
            case STATUS_PENDING:
            case STATUS_SUPERVISOR:
                // 主管审批阶段：货运主管、渠道主管、渠道副总支行、法诉主管可以审批
                return "clerk_supervisor".equals(userRole) || "channel_manager".equals(userRole) ||
                       "channel_office".equals(userRole) || "judicial_director".equals(userRole);
            case STATUS_DIRECTOR:
                // 总监审批阶段：总监可以审批
                return "director".equals(userRole);
            case STATUS_FINANCE_SUPERVISOR:
                // 财务主管审批阶段：财务主管可以审批（这里用总经理角色代替，因为没有专门的财务主管角色）
                return "manager".equals(userRole);
            case STATUS_GENERAL_MANAGER:
                // 总经理审批阶段：总经理可以审批
                return "manager".equals(userRole);
            default:
                return false;
        }
    }

    /**
     * 获取状态描述
     * @return 状态描述
     */
    public String getStatusDescription() {
        switch (this.approvalStatus) {
            case STATUS_PENDING:
                return "待审批";
            case STATUS_APPROVED:
                return "全部通过";
            case STATUS_REJECTED:
                return "已拒绝";
            case STATUS_SUPERVISOR:
                return "主管审批中";
            case STATUS_DIRECTOR:
                return "总监审批中";
            case STATUS_FINANCE_SUPERVISOR:
                return "财务主管审批中";
            case STATUS_GENERAL_MANAGER:
                return "总经理审批中";
            default:
                return "未知状态";
        }
    }
}
