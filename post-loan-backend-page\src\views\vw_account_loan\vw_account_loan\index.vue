<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="" prop="customerName">
        <el-input v-model="queryParams.customerName" placeholder="贷款人账户、姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="certId">
        <el-input v-model="queryParams.certId" placeholder="贷款人身份证号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="plateNo">
        <el-input v-model="queryParams.plateNo" placeholder="车牌号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="partnerId">
        <el-select v-model="queryParams.partnerId" placeholder="放款银行" clearable>
          <el-option v-for="dict in bankList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="jgName">
        <el-input v-model="queryParams.jgName" placeholder="录单渠道名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="followUp">
        <el-input v-model="queryParams.followUp" placeholder="跟催员" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <template v-if="showMore">
        <el-form-item label="" prop="slippageStatus">
          <el-select v-model="queryParams.slippageStatus" placeholder="逾期状态" clearable>
            <el-option v-for="dict in slippageList" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="isExtension">
          <el-select v-model="queryParams.isExtension" placeholder="是否延期" clearable>
            <el-option v-for="dict in isExtensionList" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="followStatus">
          <el-select v-model="queryParams.followStatus" placeholder="跟催类型" clearable>
            <el-option v-for="dict in followUpList" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="扣款时间">
          <el-date-picker
            v-model="queryParams.allocationTime"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
      </template>
      <el-form-item label="" prop="carStatus">
        <el-select v-model="queryParams.carStatus" placeholder="车辆状态" clearable>
          <el-option v-for="dict in carStatusList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="isFindCar">
        <el-select v-model="queryParams.isFindCar" placeholder="是否派单找车" clearable>
          <el-option v-for="dict in isFindCarList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="text" size="mini" @click="showMore = !showMore">
          {{ showMore ? '收起' : '更多' }}
          <i :class="showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
        </el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="vw_account_loanList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="55" fixed="left" />
      <el-table-column label="逾期状态" align="center" prop="slippageStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.slippageStatus != null">
            {{
              scope.row.slippageStatus == 1
                ? '提醒'
                : scope.row.slippageStatus == 2
                  ? '电催'
                  : scope.row.slippageStatus == 3
                    ? '上访'
                    : scope.row.slippageStatus == 4
                      ? '逾期30-60'
                      : '逾期60+'
            }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="还款状态" align="center" prop="repaymentStatus" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.repaymentStatus != null">
            {{
              scope.row.repaymentStatus == 1
                ? '还款中'
                : scope.row.repaymentStatus == 2
                  ? '已完结'
                  : scope.row.repaymentStatus == 3
                    ? '提前结清'
                    : scope.row.repaymentStatus == 4
                      ? '逾期催回结清'
                      : scope.row.repaymentStatus == 5
                        ? '逾期减免结清'
                        : scope.row.repaymentStatus == 6
                          ? '逾期未还款'
                          : scope.row.repaymentStatus == 7
                            ? '逾期还款中'
                            : scope.row.repaymentStatus == 8
                              ? '代偿未还款'
                              : scope.row.repaymentStatus == 9
                                ? '代偿还款中'
                                : scope.row.repaymentStatus == 10
                                  ? '代偿减免结清'
                                  : scope.row.repaymentStatus == 11
                                    ? '代偿全额结清'
                                    : '未知状态'
            }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="延期状态" align="center" prop="isExtension">
        <template slot-scope="scope">
          <span style="color: red" v-if="scope.row.isExtension == 1">延期</span>
          <span v-else-if="scope.row.isExtension == 0">未延期</span>
        </template>
      </el-table-column>

      <el-table-column label="跟催员" align="center" prop="followUp">
        <template slot-scope="scope">
          <span v-if="scope.row.followUp">{{ scope.row.followUp }}</span>
        </template>
      </el-table-column>

      <el-table-column label="贷款人" align="center" prop="customerName">
        <template slot-scope="scope">
          <span
            v-if="scope.row.customerName"
            style="color: #46a6ff; cursor: pointer"
            @click="checkLender({ customerId: scope.row.customerId, applyId: scope.row.applyId })">
            {{ scope.row.customerName }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="贷款人身份证" align="center" prop="certId" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.certId">{{ scope.row.certId }}</span>
        </template>
      </el-table-column>

      <el-table-column label="出单渠道" align="center" prop="jgName" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.jgName">{{ scope.row.jgName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="业务员" align="center" prop="nickName">
        <template slot-scope="scope">
          <span v-if="scope.row.nickName">{{ scope.row.nickName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="车牌号码" align="center" prop="plateNo" width="130">
        <template slot-scope="scope">
          <el-button v-if="scope.row.plateNo" type="text" @click="checkCar(scope.row.plateNo)">{{ scope.row.plateNo }}</el-button>
        </template>
      </el-table-column>

      <el-table-column label="车辆状态" align="center" prop="carStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.carStatus != null">
            {{
              scope.row.carStatus == '1'
                ? '省内正常行驶'
                : scope.row.carStatus == '2'
                  ? '省外正常行驶'
                  : scope.row.carStatus == '3'
                    ? '抵押'
                    : scope.row.carStatus == '4'
                      ? '疑似抵押'
                      : scope.row.carStatus == '5'
                        ? '疑似黑车'
                        : scope.row.carStatus == '6'
                          ? '已入库'
                          : scope.row.carStatus == '7'
                            ? '车在法院'
                            : scope.row.carStatus == '8'
                              ? '已法拍'
                              : '协商卖车'
            }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="车辆位置" align="center" prop="carDetailAddress" width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.carDetailAddress">{{ scope.row.carDetailAddress }}</span>
        </template>
      </el-table-column>

      <el-table-column label="GPS状态" align="center" prop="gpsStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.gpsStatus != null">
            {{
              scope.row.gpsStatus == '1'
                ? '部分拆除'
                : scope.row.gpsStatus == '2'
                  ? '全部拆除'
                  : scope.row.gpsStatus == '3'
                    ? 'GPS正常'
                    : '停车30天以上'
            }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="派车团队" align="center" prop="carTeamName">
        <template slot-scope="scope">
          <span v-if="scope.row.carTeamName">{{ scope.row.carTeamName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="放款银行" align="center" prop="bank">
        <template slot-scope="scope">
          <span v-if="scope.row.bank">{{ scope.row.bank }}</span>
        </template>
      </el-table-column>



      <el-table-column label="银行逾期天数" align="center" prop="boverdueDays" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.boverdueDays != null">{{ scope.row.boverdueDays }}</span>
        </template>
      </el-table-column>

      <el-table-column label="首期逾期金额" align="center" prop="foverdueAmount" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.foverdueAmount != null">{{ scope.row.foverdueAmount }}</span>
        </template>
      </el-table-column>

      <el-table-column label="银行逾期金额" align="center" prop="boverdueAmount" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.boverdueAmount != null">{{ scope.row.boverdueAmount }}</span>
        </template>
      </el-table-column>

      <el-table-column label="代扣逾期天数" align="center" prop="doverdueDays" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.doverdueDays != null">{{ scope.row.doverdueDays }}</span>
        </template>
      </el-table-column>

      <el-table-column label="代扣逾期金额" align="center" prop="doverdueAmount" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.doverdueAmount != null">{{ scope.row.doverdueAmount }}</span>
        </template>
      </el-table-column>

      <el-table-column label="催回金额" align="center" prop="realReturnMoney">
        <template slot-scope="scope">
          <span v-if="scope.row.realReturnMoney != null" style="color: #46a6ff; cursor: pointer" @click="checkReminder(scope.row.loanId)">
            {{ scope.row.realReturnMoney }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="催记类型" align="center" prop="followStatus">
        <template slot-scope="scope">
          <span v-if="scope.row.followStatus != null">
            {{ scope.row.followStatus == 1 ? '继续联系' : scope.row.followStatus == 2 ? '约定还款' : '无法跟进' }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="催记提交日期" align="center" prop="followDate" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.followDate">{{ parseTime(scope.row.followDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

      <el-table-column label="下次跟进时间" align="center" prop="trackingTime" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.trackingTime">
            {{ parseTime(scope.row.trackingTime, '{y}-{m}-{d}') }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="预计还款日期" align="center" prop="appointedTime" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.appointedTime">
            {{ parseTime(scope.row.appointedTime, '{y}-{m}-{d}') }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="催回日期" align="center" prop="reminderDate" width="130">
        <template slot-scope="scope">
          <span v-if="scope.row.reminderDate">{{ parseTime(scope.row.reminderDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-popover placement="left" trigger="click" popper-class="custom-popover">
            <div class="operation-buttons">
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="handleSubmitReminder(scope.row)"
                v-hasPermi="['loan_reminder:loan_reminder:add']">
                提交催记
              </el-button>
              <el-button class="operation-btn" size="mini" type="text" @click="logView(scope.row)">催记日志</el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="initiate(scope.row)"
                v-hasPermi="['vw_account_loan:vw_account_loan:initiate']">
                发起代偿
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="urgeBackSettle(scope.row)"
                v-hasPermi="['vw_account_loan:vw_account_loan:collect']">
                催回结清
              </el-button>
              <el-button
                class="operation-btn"
                size="mini"
                type="text"
                @click="derateSettle(scope.row)"
                v-hasPermi="['vw_account_loan:vw_account_loan:derate']">
                减免结清
              </el-button>
              <el-button class="operation-btn" size="mini" type="text" @click="planBth(scope.row)">还款计划</el-button>
              <el-popconfirm title="确认预估呆账吗？" @confirm="handleEstimateBadDebt(scope.row)">
                <el-button slot="reference" class="operation-btn" size="mini" type="text">预估呆账</el-button>
              </el-popconfirm>
            </div>
            <el-button slot="reference" size="mini" type="text">更多</el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <el-dialog :close-on-click-modal="false" class="dialogBox" title="催记列表" :visible.sync="reminderShow" width="700px" append-to-body>
      <el-table :data="reminderList" border style="width: 100%">
        <el-table-column prop="identity" label="身份" width="130">
          <template slot-scope="scope">
            <span>
              {{
                scope.row.identity == 1
                  ? '业务员'
                  : scope.row.identity == 2
                    ? '贷后文员'
                    : scope.row.identity == 3
                      ? '电催员'
                      : scope.row.identity == 4
                        ? '上访员'
                        : scope.row.identity == 5
                          ? '强制上访员'
                          : scope.row.identity == 6
                            ? '找车员'
                            : '法诉文员'
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="customerName" label="催回人" width="120"></el-table-column>
        <el-table-column prop="bmoney" label="银行金额"></el-table-column>
        <el-table-column prop="dmoney" label="代扣金额"></el-table-column>
        <el-table-column label="总金额">
          <template slot-scope="scope">
            <span>{{ scope.row.bmoney + scope.row.dmoney }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="trackingTime" label="催回日期" width="130"></el-table-column>
        <el-table-column label="操作">
          <template>
            <span style="color: #46a6ff; cursor: pointer">详情</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog :close-on-click-modal="false" class="dialogBox" title="发起代偿" :visible.sync="commuteopen" width="900px" append-to-body>
      <div class="settle_money" @click="trialSub">发起试算</div>
      <el-form ref="trialForm" :model="trialForm" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="贷款人" prop="customerName">
              <el-input v-model="trialForm.customerName" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出单渠道" prop="orgName">
              <el-input v-model="trialForm.orgName" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="放款银行" prop="bank">
              <el-input v-model="trialForm.bank" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="放款金额" prop="loanAmount">
              <el-input v-model="trialForm.loanAmount" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="剩余本金">
              <el-input v-model="trialFormprincipal" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行逾期金额">
              <el-input v-model="trialFormboverdueAmount" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="银行利息">
              <el-input v-model="trialForminterest" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代偿总金额">
              <el-input v-model="trialFormall" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="风险金划比例" prop="fxjProportion">
              <el-input
                v-model.number="trialForm.fxjProportion"
                @input="onInputLimit('fxjProportion', $event)"
                @change="calcMoney('fxjProportion', 'fxjMoney', $event)"
                type="number"
                style="width: 100%">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="风险金划金额" prop="fxjMoney">
              <el-input v-model="trialForm.fxjMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="fxjAccount">
              <el-select v-model="trialForm.fxjAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="渠道转入比例" prop="qdProportion">
              <el-input
                v-model.number="trialForm.qdProportion"
                @input="onInputLimit('qdProportion', $event)"
                @change="calcMoney('qdProportion', 'qdMoney', $event)"
                type="number"
                style="width: 100%">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="渠道转入金额" prop="qdMoney">
              <el-input v-model="trialForm.qdMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="qdAccount">
              <el-select v-model="trialForm.qdAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="广明借比例" prop="gmjProportion">
              <el-input
                v-model.number="trialForm.gmjProportion"
                @input="onInputLimit('gmjProportion', $event)"
                @change="calcMoney('gmjProportion', 'gmjMoney', $event)"
                type="number"
                style="width: 100%">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="广明借额" prop="gmjMoney">
              <el-input v-model="trialForm.gmjMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="gmjAccount">
              <el-select v-model="trialForm.gmjAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="科技借比例" prop="kjczProportion">
              <el-input
                v-model.number="trialForm.kjjProportion"
                @input="onInputLimit('kjjProportion', $event)"
                @change="calcMoney('kjjProportion', 'kjjMoney', $event)"
                type="number"
                style="width: 100%">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="科技借额" prop="kjjMoney">
              <el-input v-model="trialForm.kjjMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="kjjAccount">
              <el-select v-model="trialForm.kjjAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="科技出资比例" prop="kjczProportion">
              <el-input
                v-model.number="trialForm.kjczProportion"
                @input="onInputLimit('kjczProportion', $event)"
                @change="calcMoney('kjczProportion', 'kjczMoney', $event)"
                type="number"
                style="width: 100%">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="科技出资金额" prop="kjczMoney">
              <el-input v-model="trialForm.kjczMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="kjczAccount">
              <el-select v-model="trialForm.kjczAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="守邦出资比例" prop="sbczProportion">
              <el-input
                v-model.number="trialForm.sbczProportion"
                @input="onInputLimit('sbczProportion', $event)"
                @change="calcMoney('sbczProportion', 'sbczMoney', $event)"
                type="number"
                style="width: 100%">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="守邦出资金额" prop="sbczMoney">
              <el-input v-model="trialForm.sbczMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账号类型" prop="sbczAccount">
              <el-select v-model="trialForm.sbczAccount" placeholder="账号类型" clearable>
                <el-option v-for="dict in accountList" :key="dict.card" :label="dict.name" :value="dict.card" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="上传凭据">
              <div v-if="trialForm.examineStatus >= 1">
                <template v-if="trialForm.image && trialForm.image.length > 0">
                  <el-image
                    v-for="(url, index) in trialForm.image"
                    :key="index"
                    style="width: 100px; height: 100px; margin-right: 10px"
                    :src="url"
                    fit="cover"
                    :preview-src-list="trialForm.image"></el-image>
                </template>
                <el-upload v-else list-type="picture-card" :disabled="true" :action="uploadImgUrl">
                  <div>暂无凭据</div>
                </el-upload>
              </div>
              <el-upload
                v-else
                :data="data"
                :action="uploadImgUrl"
                list-type="picture-card"
                :headers="headers"
                :file-list="trialForm.image"
                :on-preview="handlePictureCardPreview"
                :on-success="(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'trialForm.image')"
                :on-remove="(file, fileList) => handleRemove(file, fileList, 'trialForm.image')"
                :on-error="handleUploadError">
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="代扣剩余未还金额">
              <el-input v-model="trialFormdoverdueAmount" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="违约金">
              <el-input v-model="trialFormliquidatedDamages" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="登记其他欠款金额" prop="otherDebt">
              <el-input type="number" @input="handleInput3" v-model="trialFormotherDebt" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总账款">
              <el-input v-model="trialFormtotal" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!trialForm.examineStatus" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancelcommute">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 催回结清对话框 -->
    <el-dialog
      :before-close="cancelurgeBack"
      :close-on-click-modal="false"
      class="dialogBox"
      title="催回结清"
      :visible.sync="urgeBackopen"
      width="800px"
      append-to-body>
      <div class="settle_money" @click="getUrgeMoney(1)" v-if="urgeform.examineStatus < 1">获取结清金额</div>
      <el-form ref="urgeform" :model="urgeform" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="贷款人" prop="customerName">
              <el-input v-model="urgeform.customerName" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出单渠道" prop="orgName">
              <el-input v-model="urgeform.orgName" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="放款银行" prop="bank">
              <el-input v-model="urgeform.bank" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="放款金额" prop="loanAmount">
              <el-input v-model="urgeform.loanAmount" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="银行结清金额" prop="btotalMoney">
              <el-input v-model="urgeformbtotalMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber1">
              <el-select v-model="urgeform.accountNumber1" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="代扣剩余未还金额" prop="dtotalMoney">
              <el-input v-model="urgeformdtotalMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber2">
              <el-select v-model="urgeform.accountNumber2" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="违约金" prop="liquidatedDamages">
              <el-input v-model="urgeformliquidatedDamages" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber3">
              <el-select v-model="urgeform.accountNumber3" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="其他欠款" prop="otherDebt">
              <el-input @input="handleInput1" v-model="urgeform.otherDebt" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber4">
              <el-select v-model="urgeform.accountNumber4" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单期代偿金" prop="oneCommutation">
              <el-input v-model="urgeformoneCommutation" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber5">
              <el-select v-model="urgeform.accountNumber5" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="总欠款金额">
              <el-input v-model="urgeAllMoney" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-if="urgeform.examineStatus <= 1" @click="submitUrge">确 定</el-button>
        <el-button @click="cancelurgeBack">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 减免结清对话框 -->
    <el-dialog
      :before-close="cancelderate"
      :close-on-click-modal="false"
      class="dialogBox"
      title="减免结清"
      :visible.sync="derateopen"
      width="800px"
      append-to-body>
      <div class="settle_money" @click="getUrgeMoney(2)" v-if="urgeform.examineStatus < 1">获取结清金额</div>
      <el-form ref="derateform" :model="derateform" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="贷款人" prop="customerName">
              <el-input v-model="derateform.customerName" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出单渠道" prop="orgName">
              <el-input v-model="derateform.orgName" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="放款银行" prop="bank">
              <el-input v-model="derateform.bank" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="放款金额" prop="loanAmount">
              <el-input v-model="derateform.loanAmount" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="银行结清金额" prop="btotalMoney">
              <el-input v-model="derateformbtotalMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber1">
              <el-select v-model="derateform.accountNumber1" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="代扣剩余未还金额" prop="dtotalMoney">
              <el-input v-model="derateformdtotalMoney" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber2">
              <el-select v-model="derateform.accountNumber2" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="违约金" prop="liquidatedDamages">
              <el-input v-model="derateformliquidatedDamages" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber3">
              <el-select v-model="derateform.accountNumber3" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="其他欠款" prop="otherDebt">
              <el-input @input="handleInput2" v-model="derateform.otherDebt" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber4">
              <el-select v-model="derateform.accountNumber4" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单期代偿金" prop="oneCommutation">
              <el-input v-model="derateformoneCommutation" :disabled="true" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="accountNumber5">
              <el-select v-model="derateform.accountNumber5" placeholder="账号类型">
                <el-option v-for="dict in accountList" :key="dict.name" :label="dict.name" :value="dict.name" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="总欠款金额">
              <el-input v-model="derateAllMoney" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-if="derateform.examineStatus <= 1" @click="submitDerate">确 定</el-button>
        <el-button @click="cancelderate">取 消</el-button>
      </div>
    </el-dialog>
    <userInfo ref="userInfo" :visible.sync="lenderShow" title="贷款人信息" :customerInfo="customerInfo" />
    <car-info ref="carInfo" :visible.sync="carShow" title="车辆信息" :plateNo="plateNo" permission="1" />
    <!-- 催记日志组件 -->
    <loan-reminder-log ref="loanReminderLog" :loan-id="logLoanId" />
    <!-- 提交催记组件 -->
    <loan-reminder-log-submit ref="loanReminderLogSubmit" :loan-id="submitLoanId" :status="3" />
  </div>
</template>

<script>
import {
  trial_balance_put,
  trial_submit_order,
  trial_balance_post,
  trial_balance_order,
  loan_reminder_order,
  listVw_account_loan,
  delVw_account_loan,
  loan_compensation_order,
  add_Trial_order,
  dc_submit_order,
  sub_Trial_order,
  update_loan_list,
  get_bank_account,
} from '@/api/vw_account_loan/vw_account_loan'
import { getToken } from '@/utils/auth'
import userInfo from '@/layout/components/Dialog/userInfo.vue'
import carInfo from '@/layout/components/Dialog/carInfo.vue'
import LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'
import LoanReminderLogSubmit from '@/layout/components/Dialog/loanReminderLogSubmit.vue'

export default {
  components: {
    userInfo,
    carInfo,
    LoanReminderLog,
    LoanReminderLogSubmit,
  },
  props: {
    value: [String, Object, Array],
    // 上传接口地址
    action: {
      type: String,
      // default: "/common/upload"
      default: '/common/ossupload',
    },
    // 上传携带的参数
    data: {
      type: Object,
    },
  },
  name: 'Vw_account_loan',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 控制更多筛选条件显示
      showMore: false,
      // 总条数
      total: 0,
      // VIEW表格数据
      vw_account_loanList: [],
      // 是否显示弹出层
      logopen: false, //催记日志
      currentRow: {}, //dialog传入数据
      urgeBackopen: false, //催回结清
      derateopen: false, //减免结清
      commuteopen: false, //发起代偿
      detailShow: false, //查看日志详情
      lenderShow: false, //贷款人信息
      customerInfo: {
        customerId: '',
        applyId: '',
      },
      carShow: false, // 车辆信息
      plateNo: '', // 车辆编号
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 15,
        customerName: null,
        certId: null,
        plateNo: null,
        partnerId: null,
        jgName: null,
        slippageStatus: null,
        followStatus: null,
        followUp: null,
        allocationTime: null,
        startTime: '',
        endTime: '',
        isExtension: '',
        carStatus: '',
        isFindCar: '',
      },

      bankList: [
        { value: 'EO00000010', label: '苏银金租' },
        { value: 'IO00000006', label: '浙商银行' },
        { value: 'IO00000007', label: '中关村银行' },
        { value: 'IO00000008', label: '蓝海银行' },
        { value: 'IO00000009', label: '华瑞银行' },
        { value: 'IO00000010', label: '皖新租赁' },
      ],

      isExtensionList: [
        { label: '否', value: '0' },
        { label: '是', value: '1' },
      ],
      slippageList: [
        { label: '提醒', value: 1 },
        { label: '电催', value: 2 },
        { label: '上访', value: 3 },
        { label: '逾期30-60', value: 4 },
        { label: '逾期60+', value: 5 },
      ],
      followUpList: [
        { label: '继续联系', value: 1 },
        { label: '约定还款', value: 2 },
        { label: '无法跟进', value: 3 },
      ],
      accountList: [], //银行账户列表
      // 表单校验
      rules: {},
      dialogImageUrl: '',
      dialogVisible: false,
      textarea: null,
      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action, // 上传的图片服务器地址
      headers: {
        Authorization: 'Bearer ' + getToken(),
      },
      reminderList: [], //催记列表
      reminderShow: false,
      urgeform: {},
      derateform: {},
      radio: 0,
      logList: [], //催记日志
      logDetail: {}, //催记日志详情
      logForm: {
        loanId: null,
        pageNum: 1,
        pageSize: 15,
      },
      logtotal: 0,
      urgeAllMoney: 0, //催回结清总欠款
      derateAllMoney: 0, //减免结清总欠款
      loanId: null,
      addId: null, //催回、减免新增id
      jmForm: {},
      chForm: {},
      urgeformbtotalMoney: 0,
      urgeformdtotalMoney: 0,
      urgeformliquidatedDamages: 0,
      urgeformoneCommutation: 0,
      derateformbtotalMoney: 0,
      derateformdtotalMoney: 0,
      derateformliquidatedDamages: 0,
      derateformoneCommutation: 0,
      trialForm: {},
      trialFormprincipal: 0,
      trialFormboverdueAmount: 0,
      trialForminterest: 0,
      trialFormall: 0,
      trialFormdoverdueAmount: 0,
      trialFormliquidatedDamages: 0,
      trialFormtotal: 0,
      trialFormotherDebt: 0,
      dkyqMoney: 0,
      bankyqMoney: 0,
      lendingBank: null,
      OrderingChannel: null,
      logLoanId: null, //嵌入催记日志组件的loanId
      submitLoanId: null, //嵌入提交催记组件的loanId
      carStatusList: [
        { label: '省内正常行驶', value: '1' },
        { label: '省外正常行驶', value: '2' },
        { label: '抵押', value: '3' },
        { label: '疑似抵押', value: '4' },
        { label: '疑似黑车', value: '5' },
        { label: '已入库', value: '6' },
        { label: '车在法院', value: '7' },
        { label: '已法拍', value: '8' },
        { label: '协商卖车', value: '9' },
      ],
      isFindCarList: [
        { label: '未派单', value: '0' },
        { label: '已派单', value: '1' },
      ],
    }
  },
  created() {
    this.getList()
    this.getAccountList()
  },
  methods: {
    getAccountList() {
      get_bank_account().then(response => {
        this.accountList = response.rows
      })
    },
    handleEstimateBadDebt(row) {
      const data = {
        id: row.loanId,
        badDebt: 1,
      }
      update_loan_list(data).then(response => {
        if (response.code == 200) {
          this.$modal.msgSuccess('预估呆账成功')
        } else {
          this.$modal.msgError('预估呆账失败')
        }
      })
    },
    trialSub() {
      var data = {
        applyId: this.trialForm.applyId,
        id: this.trialForm.id,
        loanId: this.trialForm.loanId,
        loanAmount: this.trialForm.loanAmount,
        partnerId: this.trialForm.partnerId,
      }
      dc_submit_order(data).then(response => {
        this.trialFormprincipal = response.data.principal || 0
        this.trialForm.defaultInterest = response.data.defaultInterest || 0
        this.trialForminterest = response.data.interest || 0
        this.trialFormall = response.data.btotalMoney || 0
        this.trialFormdoverdueAmount = response.data.dtotalMoney || 0
        this.trialFormliquidatedDamages = response.data.liquidatedDamages || 0
        this.trialFormtotal = Number(
          this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt
        ).toFixed(2)
      })
    },
    submitDerate() {
      if (this.derateformbtotalMoney && !this.derateform.accountNumber1) {
        this.$modal.msgError('请选择银行结清账号')
        return
      }
      if (this.derateformdtotalMoney && !this.derateform.accountNumber2) {
        this.$modal.msgError('请选择代扣剩余账号')
        return
      }
      if (this.derateformliquidatedDamages && !this.derateform.accountNumber3) {
        this.$modal.msgError('请选择违约金账号')
        return
      }
      if (this.derateform.otherDebt && !this.derateform.accountNumber4) {
        this.$modal.msgError('请选择其他欠款账号')
        return
      }
      if (this.derateformoneCommutation && !this.derateform.accountNumber5) {
        this.$modal.msgError('请选择单期代偿金账号')
        return
      }
      var data = {
        customerName: this.derateform.customerName,
        orgName: this.derateform.orgName,
        bank: this.derateform.bank,
        loanAmount: this.derateform.loanAmount,
        btotalMoney: this.derateform.btotalMoney,
        accountNumber1: this.derateform.accountNumber1,
        dtotalMoney: this.derateform.dtotalMoney,
        accountNumber2: this.derateform.accountNumber2,
        liquidatedDamages: this.derateform.liquidatedDamages,
        accountNumber3: this.derateform.accountNumber3,
        otherDebt: this.derateform.otherDebt,
        accountNumber4: this.derateform.accountNumber4,
        oneCommutation: this.derateform.oneCommutation,
        accountNumber5: this.derateform.accountNumber5,
        totalMoney: this.derateAllMoney,
        id: this.addId,
        status: 2,
        examineStatus: 1,
      }
      trial_balance_put(data).then(response => {
        if (response.code == 200) {
          this.$modal.msgSuccess('提交成功')
          this.derateopen = false
          this.derateform = {}
          this.derateformbtotalMoney = 0
          this.derateformdtotalMoney = 0
          this.derateformliquidatedDamages = 0
          this.derateformoneCommutation = 0
        }
      })
    },
    submitUrge() {
      if (this.urgeformbtotalMoney && !this.urgeform.accountNumber1) {
        this.$modal.msgError('请选择银行结清账号')
        return
      }
      if (this.urgeformdtotalMoney && !this.urgeform.accountNumber2) {
        this.$modal.msgError('请选择代扣剩余账号')
        return
      }
      if (this.urgeformliquidatedDamages && !this.urgeform.accountNumber3) {
        this.$modal.msgError('请选择违约金账号')
        return
      }
      if (this.urgeform.otherDebt && !this.urgeform.accountNumber4) {
        this.$modal.msgError('请选择其他欠款账号')
        return
      }
      if (this.urgeformoneCommutation && !this.urgeform.accountNumber5) {
        this.$modal.msgError('请选择单期代偿金账号')
        return
      }
      var data = {
        customerName: this.urgeform.customerName,
        orgName: this.urgeform.orgName,
        bank: this.urgeform.bank,
        loanAmount: this.urgeform.loanAmount,
        btotalMoney: this.urgeform.btotalMoney,
        accountNumber1: this.urgeform.accountNumber1,
        dtotalMoney: this.urgeform.dtotalMoney,
        accountNumber2: this.urgeform.accountNumber2,
        liquidatedDamages: this.urgeform.liquidatedDamages,
        accountNumber3: this.urgeform.accountNumber3,
        otherDebt: this.urgeform.otherDebt,
        accountNumber4: this.urgeform.accountNumber4,
        oneCommutation: this.urgeform.oneCommutation,
        accountNumber5: this.urgeform.accountNumber5,
        totalMoney: this.urgeAllMoney,
        id: this.addId,
        status: 1,
        examineStatus: 1,
      }
      trial_balance_put(data).then(response => {
        if (response.code == 200) {
          this.$modal.msgSuccess('提交成功')
          this.urgeBackopen = false
          this.urgeform = {}
          this.urgeformbtotalMoney = 0
          this.urgeformdtotalMoney = 0
          this.urgeformliquidatedDamages = 0
          this.urgeformoneCommutation = 0
        }
      })
    },
    onInputLimit(key, value, min = 0, max = 100) {
      let val = Number(value)
      if (isNaN(val)) val = min
      if (val > max) val = max
      if (val < min) val = min
      this.trialForm[key] = Number(val)
    },
    cancelDetail() {
      this.detailShow = false
    },
    //查看车牌信息
    checkCar(plateNo) {
      this.plateNo = plateNo
      this.carShow = true
    },
    //查看贷款人信息
    checkLender(customerInfo) {
      this.customerInfo = customerInfo
      this.lenderShow = true
    },
    planBth(row) {
      var data = {
        partnerId: row.partnerId,
        applyId: row.applyId,
      }
      if (row.partnerId == 'IO00000008') {
        this.$router.push({
          path: '/repayment/repayment_plan/lhindex',
          query: data,
        })
      } else if (row.partnerId == 'EO00000010') {
        this.$router.push({
          path: '/repayment/repayment_plan/syindex',
          query: data,
        })
      } else if (row.partnerId == 'IO00000006') {
        this.$router.push({
          path: '/repayment/repayment_plan/zsindex',
          query: data,
        })
      } else if (row.partnerId == 'IO00000007') {
        this.$router.push({
          path: '/repayment/repayment_plan/zgcindex',
          query: data,
        })
      } else if (row.partnerId == 'IO00000009') {
        this.$router.push({
          path: '/repayment/repayment_plan/hrindex',
          query: data,
        })
      } else {
        this.$router.push({
          path: '/repayment/repayment_plan/wxindex',
          query: data,
        })
      }
    },
    checkReminder(id) {
      var data = {
        loanId: id,
        urgeStatus: 2,
      }
      loan_reminder_order(data).then(response => {
        this.reminderList = response.rows
        this.reminderShow = true
      })
    },

    cancelcommute() {
      this.commuteopen = false
    },
    initiate(row) {
      //发起代偿
      var data = {
        loanId: row.loanId,
      }
      loan_compensation_order(data).then(response => {
        if (response.data) {
          this.trialForm = response.data
          this.trialForm.loanAmount = row.contractAmt || 0
          this.trialForm.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0
          this.trialFormprincipal = response.data.trialBalance ? response.data.trialBalance.principal : 0
          this.trialFormboverdueAmount = row.boverdueAmount || 0
          this.trialForminterest = response.data.trialBalance ? response.data.trialBalance.interest : 0
          this.trialFormall = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0
          this.trialFormdoverdueAmount = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0
          this.trialFormliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0
          this.trialFormotherDebt = response.data.otherDebt ? response.data.otherDebt : 0
          console.log(this.trialFormall, this.trialFormdoverdueAmount, this.trialFormliquidatedDamages, this.trialFormotherDebt)
          this.trialFormtotal = Number(
            this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt
          ).toFixed(2)
          this.trialForm.image = response.data.image ? response.data.image.split(',') : []

          this.commuteopen = true
        } else {
          this.addTrial(row)
          this.trialForm.image = []
        }
      })
    },
    addTrial(row) {
      console.log(row)
      var data = {
        id: row.id,
        applyId: row.applyId,
        loanId: row.loanId,
        customerId: row.customerId,
        customerName: row.customerName,
        salesman: row.nickName,
        orgName: row.jgName,
        partnerId: row.partnerId,
        bank: row.orgName,
        loanAmount: row.contractAmt,
        examineStatus: 0,
      }
      add_Trial_order(data).then(response => {
        this.trialForm = response.data || {}
        this.commuteopen = true
      })
    },
    cancelderate() {
      this.derateform = {}
      this.jmForm = {}
      this.derateformbtotalMoney = 0
      this.derateformdtotalMoney = 0
      this.derateformliquidatedDamages = 0
      this.derateformoneCommutation = 0
      this.derateAllMoney = 0
      this.derateopen = false
    },
    handleInput3(value) {
      this.trialFormotherDebt = Number(value)
      this.trialFormtotal = Number(
        this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt
      ).toFixed(2)
    },
    handleInput2(value) {
      this.derateform.otherDebt = Number(value)
      this.derateAllMoney = Number(
        this.derateform.btotalMoney +
          this.derateform.dtotalMoney +
          this.derateform.liquidatedDamages +
          this.derateform.otherDebt +
          this.derateform.oneCommutation
      ).toFixed(2)
    },
    handleInput1(value) {
      this.urgeform.otherDebt = Number(value)
      this.urgeAllMoney = Number(
        this.urgeform.btotalMoney +
          this.urgeform.dtotalMoney +
          this.urgeform.liquidatedDamages +
          this.urgeform.otherDebt +
          this.urgeform.oneCommutation
      ).toFixed(2)
    },
    derateSettle(row) {
      var data = {
        loanId: row.loanId,
        status: 2,
        pageSize: 1,
        pageNum: 1,
      }
      this.loanId = row.loanId
      trial_balance_order(data)
        .then(response => {
          if (response.data) {
            this.derateform = response.data
            if (!response.data.customerName) {
              this.derateform.customerName = row.customerName
            }
            if (!response.data.orgName) {
              this.derateform.orgName = row.jgName
            }
            if (!response.data.bank) {
              this.derateform.bank = row.orgName
            }
            if (!response.data.loanAmount) {
              this.derateform.loanAmount = row.contractAmt
            }
            this.derateform.btotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0
            this.derateform.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0
            this.derateform.oneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0
            this.derateform.liquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0
            this.derateformbtotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0
            this.derateformdtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0
            this.derateformoneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0
            this.derateformliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0
            this.derateAllMoney = Number(
              this.derateform.btotalMoney + this.derateform.dtotalMoney + this.derateform.liquidatedDamages + this.derateform.oneCommutation
            ).toFixed(2)
            this.addId = response.data.id
            this.jmForm = response.data
          } else {
            this.postTrial(row, 2)
            // this.derateform = {}
            // this.derateform.customerName = row.customerName
            // this.derateform.orgName = row.jgName
            // this.derateform.bank = row.orgName
            // this.derateform.loanAmount = row.contractAmt
          }
        })
        .catch(error => {})
      this.derateopen = true
    },
    cancelurgeBack() {
      this.urgeform = {}
      this.chForm = {}
      this.urgeformbtotalMoney = 0
      this.urgeformdtotalMoney = 0
      this.urgeformliquidatedDamages = 0
      this.urgeformoneCommutation = 0
      this.urgeAllMoney = 0
      this.urgeBackopen = false
    },
    getUrgeMoney(e) {
      var data = {}
      if (e == 1) {
        data = this.chForm
      } else {
        data = this.jmForm
      }
      trial_submit_order(data).then(response => {
        if (e == 1) {
          // this.urgeform = response.data
          this.urgeform.btotalMoney = response.data.btotalMoney || 0
          this.urgeform.dtotalMoney = response.data.dtotalMoney || 0
          this.urgeform.liquidatedDamages = response.data.liquidatedDamages || 0
          this.urgeform.oneCommutation = response.data.oneCommutation || 0
          this.urgeformbtotalMoney = response.data.btotalMoney || 0
          this.urgeformdtotalMoney = response.data.dtotalMoney || 0
          this.urgeformliquidatedDamages = response.data.liquidatedDamages || 0
          this.urgeformoneCommutation = response.data.oneCommutation || 0
          this.urgeAllMoney = Number(
            this.urgeform.btotalMoney +
              this.urgeform.dtotalMoney +
              this.urgeform.liquidatedDamages +
              this.urgeform.otherDebt +
              this.urgeform.oneCommutation
          ).toFixed(2)
        } else {
          // this.derateform = response.data
          this.derateform.btotalMoney = response.data.btotalMoney || 0
          this.derateform.dtotalMoney = response.data.dtotalMoney || 0
          this.derateform.liquidatedDamages = response.data.liquidatedDamages || 0
          this.derateform.oneCommutation = response.data.oneCommutation || 0
          this.derateformbtotalMoney = response.data.btotalMoney || 0
          this.derateformdtotalMoney = response.data.dtotalMoney || 0
          this.derateformliquidatedDamages = response.data.liquidatedDamages || 0
          this.derateformoneCommutation = response.data.oneCommutation || 0
          this.derateAllMoney = Number(
            this.derateform.btotalMoney +
              this.derateform.dtotalMoney +
              this.derateform.liquidatedDamages +
              this.derateform.otherDebt +
              this.derateform.oneCommutation
          ).toFixed(2)
        }
      })
    },
    urgeBackSettle(row) {
      var data = {
        loanId: row.loanId,
        status: 1,
        pageSize: 1,
        pageNum: 1,
      }
      this.loanId = row.loanId
      trial_balance_order(data)
        .then(response => {
          if (response.data) {
            this.urgeform = response.data
            if (!response.data.customerName) {
              this.urgeform.customerName = row.customerName
            }
            if (!response.data.orgName) {
              this.urgeform.orgName = row.jgName
            }
            if (!response.data.bank) {
              this.urgeform.bank = row.orgName
            }
            if (!response.data.loanAmount) {
              this.urgeform.loanAmount = row.contractAmt
            }
            this.urgeform.btotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0
            this.urgeform.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0
            this.urgeform.liquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0
            this.urgeform.oneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0
            this.urgeformbtotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0
            this.urgeformdtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0
            this.urgeformliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0
            this.urgeformoneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0
            this.urgeAllMoney = Number(
              this.urgeform.btotalMoney + this.urgeform.dtotalMoney + this.urgeform.liquidatedDamages + this.urgeform.oneCommutation
            ).toFixed(2)
            this.addId = response.data.id
            this.chForm = response.data
          } else {
            this.postTrial(row, 1)
            // this.urgeform = {}
            // this.urgeform.customerName = row.customerName
            // this.urgeform.orgName = row.jgName
            // this.urgeform.bank = row.orgName
            // this.urgeform.loanAmount = row.contractAmt
          }
          this.urgeBackopen = true
        })
        .catch(error => {})
    },
    postTrial(row, e) {
      var data = {
        applyId: row.applyId,
        loanId: row.loanId,
        customerName: row.customerName,
        orgName: row.jgName,
        loanAmount: row.contractAmt,
        bank: row.orgName,
        partnerId: row.partnerId,
        status: e,
        salesman: row.nickName,
        overdueDays: row.boverdueDays,
        examineStatus: 0,
      }
      trial_balance_post(data).then(response => {
        this.addId = response.id
        if (e == 1) {
          this.urgeform = response.data
          this.chForm = response.data
        } else {
          this.derateform = response.data
          this.jmForm = response.data
        }
      })
    },
    cancelLog() {
      this.logopen = false
    },
    logView(row) {
      this.logLoanId = row.loanId
      this.$refs.loanReminderLog.openLogDialog()
    },
    handleSubmitReminder(row) {
      this.submitLoanId = row.loanId
      this.$nextTick(() => {
        this.$refs.loanReminderLogSubmit.openDialog()
      })
    },
    logView2() {
      loan_reminder_order(this.logForm)
        .then(response => {
          this.logList = response.rows
          this.logtotal = response.total
          this.logopen = true
        })
        .catch(error => {})
    },

    handleChange(value) {
      this.queryParams.orgId = value
    },
    /** 查询VIEW列表 */
    getList() {
      console.log(this.$store.state.user)
      this.loading = true
      listVw_account_loan(this.queryParams).then(response => {
        this.vw_account_loanList = response.rows
        this.total = response.total
        this.loading = false
      })
    },

    // 表单重置
    reset() {
      this.form = {
        customerName: null,
        jgName: null,
        orgName: null,
        boverdueAmount: null,
        doverdueAmount: null,
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.allocationTime) {
        this.queryParams.startTime = this.queryParams.allocationTime[0]
        this.queryParams.endTime = this.queryParams.allocationTime[1]
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.customerName = null
      this.queryParams.certId = null
      this.queryParams.plateNo = null
      this.queryParams.partnerId = null
      this.queryParams.jgName = null
      this.queryParams.slippageStatus = null
      this.queryParams.followStatus = null
      this.queryParams.followUp = null
      this.queryParams.allocationTime = null
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.queryParams.isExtension = null
      this.queryParams.carStatus = ''
      this.queryParams.isFindCar = ''
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加VIEW'
    },

    calcMoney(proportionKey, moneyKey, val) {
      // 只做金额计算和保留两位小数
      this.trialForm[moneyKey] = ((val * this.trialFormall) / 100).toFixed(2)
    },
    /** 提交按钮 */
    submitForm() {
      const keys = ['fxjProportion', 'qdProportion', 'gmjProportion', 'kjjProportion', 'kjczProportion', 'sbczProportion']
      // 计算比例之和
      const total = keys.reduce((sum, key) => sum + Number(this.trialForm[key] || 0), 0)
      if (total !== 100) {
        this.$message.error('六项比例之和必须等于100！')
        return
      }

      // 金额和账号类型字段映射
      const moneyAccountMap = [
        { money: 'fxjMoney', account: 'fxjAccount', label: '风险金账号类型' },
        { money: 'qdMoney', account: 'qdAccount', label: '渠道账号类型' },
        { money: 'gmjMoney', account: 'gmjAccount', label: '广明借账号类型' },
        { money: 'kjjMoney', account: 'kjjAccount', label: '科技借账号类型' },
        {
          money: 'kjczMoney',
          account: 'kjczAccount',
          label: '科技出资账号类型',
        },
        {
          money: 'sbczMoney',
          account: 'sbczAccount',
          label: '守邦出资账号类型',
        },
      ]

      // 校验每个金额>0时账号类型必填
      for (const item of moneyAccountMap) {
        if (Number(this.trialForm[item.money]) > 0 && !this.trialForm[item.account]) {
          this.$message.warning(`请先选择${item.label}`)
          return
        }
      }
      var data = {
        id: this.trialForm.id,
        fxjProportion: this.trialForm.fxjProportion,
        fxjMoney: this.trialForm.fxjMoney,
        fxjAccount: this.trialForm.fxjAccount,
        qdProportion: this.trialForm.qdProportion,
        qdMoney: this.trialForm.qdMoney,
        qdAccount: this.trialForm.qdAccount,
        gmjProportion: this.trialForm.gmjProportion,
        gmjMoney: this.trialForm.gmjMoney,
        gmjAccount: this.trialForm.gmjAccount,
        kjjProportion: this.trialForm.kjjProportion,
        kjjMoney: this.trialForm.kjjMoney,
        kjjAccount: this.trialForm.kjjAccount,
        kjczProportion: this.trialForm.kjczProportion,
        kjczMoney: this.trialForm.kjczMoney,
        kjczAccount: this.trialForm.kjczAccount,
        sbczProportion: this.trialForm.sbczProportion,
        sbczMoney: this.trialForm.sbczMoney,
        sbczAccount: this.trialForm.sbczAccount,
        otherDebt: this.trialFormotherDebt,
        totalMoney: this.trialFormtotal,
        image: this.trialForm.image.map(item => item.response).join(','),
        examineStatus: 1,
        repaymentType: 0,
      }
      console.log(data)
      sub_Trial_order(data).then(response => {
        if (response.code == 200) {
          this.$modal.msgSuccess('提交成功')
          this.commuteopen = false
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除VIEW编号为"' + ids + '"的数据项？')
        .then(function () {
          return delVw_account_loan(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'vw_account_loan/vw_account_loan/export',
        {
          ...this.queryParams,
        },
        `vw_account_loan_${new Date().getTime()}.xlsx`
      )
    },
    selectRow() {
      // Implement the logic for selecting a row
      console.log('Row selected:', this.vw_account_loanList)
    },

    // 通用的上传成功处理函数
    handleUploadSuccess(res, file, fileList, formField) {
      const [obj, prop] = formField.split('.')
      this[obj][prop] = fileList
    },
    // 通用的删除处理函数
    handleRemove(file, fileList, formField) {
      const [obj, prop] = formField.split('.')
      this[obj][prop] = fileList
    },
    // 上传失败
    handleUploadError() {
      this.$modal.msgError('上传图片失败，请重试')
      this.$modal.closeLoading()
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
  },
}
</script>
<style>
.dialogBox .el-form-item__label {
  width: 100px !important;
}

.dialogBox .el-form-item__content {
  margin-left: 100px !important;
}

.settle_money {
  background-color: #1890ff;
  color: #fff;
  border-radius: 5px;
  display: inline-block;
  padding: 5px 10px;
  box-sizing: border-box;
  margin-bottom: 10px;
}

.el-dialog__body {
  padding-top: 10px !important;
  box-sizing: border-box;
}

/* New styles for the filter bar */
.filter-container {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  margin-bottom: 10px;
  overflow-x: auto;
}

.filter-item-wrapper {
  flex: 1;
  min-width: 180px;
  padding: 0 5px;
}

.filter-item-wrapper .el-form-item {
  margin-bottom: 0;
  width: 100%;
}

.filter-item-wrapper .el-select,
.filter-item-wrapper .el-input,
.filter-item-wrapper .el-date-editor {
  width: 100% !important;
}

.filter-button-wrapper {
  display: flex;
  justify-content: flex-end;
  white-space: nowrap;
  padding-left: 10px;
  margin-left: auto;
}

.filter-button-wrapper .el-form-item {
  margin-bottom: 0;
}

.filter-row {
  margin-bottom: 10px;
}

.filter-row .el-form-item {
  margin-bottom: 10px;
  width: 100%;
}

.filter-row .el-select,
.filter-row .el-input,
.filter-row .el-date-editor {
  width: 100% !important;
}

.filter-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 10px 0;
  border-top: 1px solid #ebeef5;
}

.filter-buttons .el-button {
  margin-left: 10px;
}

/* 响应式布局调整 */
@media screen and (max-width: 1400px) {
  .filter-row .el-col-lg-4 {
    width: 33.33%;
  }
}

@media screen and (max-width: 992px) {
  .filter-row .el-col-md-8 {
    width: 50%;
  }
}

@media screen and (max-width: 768px) {
  .filter-row .el-col-sm-12 {
    width: 100%;
  }
}
/* 操作按钮容器 */
.operation-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 90px;
  padding: 4px 8px;
}

/* 操作按钮样式 */
.operation-btn {
  width: 74px !important;
  height: 26px !important;
  margin: 0 !important;
  padding: 0 2px !important;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 12px;
  white-space: nowrap;
  text-align: center;
  line-height: 26px;
}

/* 按钮悬停效果 */
.operation-btn:hover {
  background-color: #f5f7fa;
  color: #409eff;
}
</style>
<style>
.custom-popover {
  width: 116px !important;
  min-width: 116px !important;
  max-width: 116px !important;
  box-sizing: border-box !important;
}
</style>
