import request from '@/utils/request'

// 查询日常花费审批列表
export function listDaily_expense_approval(query) {
  return request({
    url: '/daily_expense_approval/daily_expense_approval/list',
    method: 'get',
    params: query
  })
}

// 查询待审批的日常花费申请列表
export function listPendingDaily_expense_approval(query) {
  return request({
    url: '/daily_expense_approval/daily_expense_approval/pendingList',
    method: 'get',
    params: query
  })
}

// 查询日常花费审批详细
export function getDaily_expense_approval(id) {
  return request({
    url: '/daily_expense_approval/daily_expense_approval/' + id,
    method: 'get'
  })
}

// 新增日常花费审批
export function addDaily_expense_approval(data) {
  return request({
    url: '/daily_expense_approval/daily_expense_approval',
    method: 'post',
    data: data
  })
}

// 修改日常花费审批
export function updateDaily_expense_approval(data) {
  return request({
    url: '/daily_expense_approval/daily_expense_approval',
    method: 'put',
    data: data
  })
}

// 删除日常花费审批
export function delDaily_expense_approval(id) {
  return request({
    url: '/daily_expense_approval/daily_expense_approval/' + id,
    method: 'delete'
  })
}

// 审批日常花费申请
export function approveDaily_expense_approval(id, data) {
  return request({
    url: '/daily_expense_approval/daily_expense_approval/approve/' + id,
    method: 'post',
    data: data
  })
}
