import request from '@/utils/request'

// 查询VIEW列表
export function listVm_car_order(query) {
  return request({
    url: '/vm_car_order/vm_car_order/list',
    method: 'get',
    params: query
  })
}
// 查询录单渠道、找车团队
export function teamVm_car_order() {
  return request({
    url: '/vm_car_order/vm_car_order/cate',
    method: 'get',
  })
}

// 查询VIEW详细
export function getVm_car_order(id) {
  return request({
    url: '/vm_car_order/vm_car_order/' + id,
    method: 'get'
  })
}

// 新增VIEW
export function addVm_car_order(data) {
  return request({
    url: '/vm_car_order/vm_car_order',
    method: 'post',
    data: data
  })
}

// 修改VIEW
export function updateVm_car_order(data) {
  return request({
    url: '/vm_car_order/vm_car_order',
    method: 'put',
    data: data
  })
}

// 删除VIEW
export function delVm_car_order(id) {
  return request({
    url: '/vm_car_order/vm_car_order/' + id,
    method: 'delete'
  })
}
// 撤销订单
export function RevokeVm_car_order(query) {
  return request({
    url: '/car_order/car_order',
    method: 'put',
    data: query
  })
}

// 提交找车费用
export function submitCarFindCost(data) {
  return request({
    url: '/vm_car_order/vm_car_order/submitCost',
    method: 'post',
    data: data
  })
}

// 邮寄钥匙
export function mailKey(data) {
  return request({
    url: '/vm_car_order/vm_car_order/mailKey',
    method: 'post',
    data: data
  })
}
