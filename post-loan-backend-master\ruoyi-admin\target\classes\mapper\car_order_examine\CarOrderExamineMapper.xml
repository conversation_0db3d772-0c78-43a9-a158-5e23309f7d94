<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.car_order_examine.mapper.CarOrderExamineMapper">
    
    <resultMap type="CarOrderExamine" id="CarOrderExamineResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="loanId"    column="loan_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="transportationFee"    column="transportation_fee"    />
        <result property="towingFee"    column="towing_fee"    />
        <result property="trackerInstallationFee"    column="tracker_installation_fee"    />
        <result property="otherReimbursement"    column="other_reimbursement"    />
        <result property="totalCost"    column="total_cost"    />
        <result property="status"    column="status"    />
        <result property="examineTime"    column="examine_time"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="currentApprover"    column="current_approver"    />
        <result property="approvalHistory"    column="approval_history"    />
    </resultMap>

    <sql id="selectCarOrderExamineVo">
        select id, apply_no, loan_id, create_by, create_date, update_by, update_date, transportation_fee, towing_fee, tracker_installation_fee, other_reimbursement, total_cost, status, examine_time, reject_reason, current_approver, approval_history from car_order_examine
    </sql>

    <select id="selectCarOrderExamineList" parameterType="CarOrderExamine" resultMap="CarOrderExamineResult">
        <include refid="selectCarOrderExamineVo"/>
        <where>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="garageId != null "> and garage_id = #{garageId}</if>
            <if test="keyStatus != null "> and key_status = #{keyStatus}</if>
            <if test="startTime != null and endTime != null">AND allocation_time BETWEEN #{startTime} AND #{endTime}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="plateNo != null  and plateNo != ''"> and plate_no = #{plateNo}</if>
            <if test="jgName != null  and jgName != ''"> and jg_name = #{jgName}</if>
            <if test="status != null"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectCarOrderExamineById" parameterType="String" resultMap="CarOrderExamineResult">
        <include refid="selectCarOrderExamineVo"/>
        where id = #{id}
    </select>

    <select id="selectCarOrderExamineByApplyNo" parameterType="String" resultMap="CarOrderExamineResult">
        <include refid="selectCarOrderExamineVo"/>
        where apply_no = #{applyNo}
    </select>

    <select id="selectCarOrderExamineByLoanId" parameterType="String" resultMap="CarOrderExamineResult">
        <include refid="selectCarOrderExamineVo"/>
        where loan_id = #{loanId}
    </select>

    <insert id="insertCarOrderExamine" parameterType="CarOrderExamine">
        insert into car_order_examine
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="loanId != null">loan_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="transportationFee != null">transportation_fee,</if>
            <if test="towingFee != null">towing_fee,</if>
            <if test="trackerInstallationFee != null">tracker_installation_fee,</if>
            <if test="otherReimbursement != null">other_reimbursement,</if>
            <if test="totalCost != null">total_cost,</if>
            <if test="status != null">status,</if>
            <if test="examineTime != null">examine_time,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="currentApprover != null">current_approver,</if>
            <if test="approvalHistory != null">approval_history,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="loanId != null">#{loanId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="transportationFee != null">#{transportationFee},</if>
            <if test="towingFee != null">#{towingFee},</if>
            <if test="trackerInstallationFee != null">#{trackerInstallationFee},</if>
            <if test="otherReimbursement != null">#{otherReimbursement},</if>
            <if test="totalCost != null">#{totalCost},</if>
            <if test="status != null">#{status},</if>
            <if test="examineTime != null">#{examineTime},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="currentApprover != null">#{currentApprover},</if>
            <if test="approvalHistory != null">#{approvalHistory},</if>
         </trim>
    </insert>

    <update id="updateCarOrderExamine" parameterType="CarOrderExamine">
        update car_order_examine
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="transportationFee != null">transportation_fee = #{transportationFee},</if>
            <if test="towingFee != null">towing_fee = #{towingFee},</if>
            <if test="trackerInstallationFee != null">tracker_installation_fee = #{trackerInstallationFee},</if>
            <if test="otherReimbursement != null">other_reimbursement = #{otherReimbursement},</if>
            <if test="totalCost != null">total_cost = #{totalCost},</if>
            <if test="status != null">status = #{status},</if>
            <if test="examineTime != null">examine_time = #{examineTime},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="currentApprover != null">current_approver = #{currentApprover},</if>
            <if test="approvalHistory != null">approval_history = #{approvalHistory},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCarOrderExamineById" parameterType="String">
        delete from car_order_examine where id = #{id}
    </delete>

    <delete id="deleteCarOrderExamineByIds" parameterType="String">
        delete from car_order_examine where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>