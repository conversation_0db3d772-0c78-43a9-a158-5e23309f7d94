<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="402e787b-5396-48de-93d1-448d61bdbbd4" name="更改" comment="合并代码3">
      <change beforePath="$PROJECT_DIR$/README_IMAGE_UPLOAD.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/TEST_IMAGE_UPLOAD.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/TEST_LOAN_COMPENSATION_FIX.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/RuoYiApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/RuoYiApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/car_order_examine/controller/CarOrderExamineController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/car_order_examine/controller/CarOrderExamineController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/car_order_examine/domain/CarOrderExamine.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/car_order_examine/domain/CarOrderExamine.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/car_order_examine/service/ICarOrderExamineService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/car_order_examine/service/ICarOrderExamineService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/car_order_examine/service/impl/CarOrderExamineServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/car_order_examine/service/impl/CarOrderExamineServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/daily_expense/controller/DailyExpenseController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/daily_expense/domain/DailyExpense.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/daily_expense/mapper/DailyExpenseMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/daily_expense/service/IDailyExpenseService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/daily_expense/service/impl/DailyExpenseServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_case/controller/LitigationCaseController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_case/controller/LitigationCaseController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_cost/controller/LitigationCostController.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_cost/controller/LitigationCostController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_cost/domain/LitigationCost.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_cost/domain/LitigationCost.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_cost/service/ILitigationCostService.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_cost/service/ILitigationCostService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_cost/service/impl/LitigationCostServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/litigation_cost/service/impl/LitigationCostServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/loan_reminder/mapper/LoanReminderMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/loan_reminder/mapper/LoanReminderMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/vw_car_order_examine/domain/VwCarOrderExamine.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/vw_car_order_examine/domain/VwCarOrderExamine.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/vw_litigation_case_full/domain/VwLitigationCaseFull.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/vw_litigation_case_full/domain/VwLitigationCaseFull.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/vw_litigation_case_full/service/impl/VwLitigationCaseFullServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/java/com/ruoyi/vw_litigation_case_full/service/impl/VwLitigationCaseFullServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/car_order_examine/CarOrderExamineMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/car_order_examine/CarOrderExamineMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/daily_expense/DailyExpenseMapper.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/litigation_cost/LitigationCostMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/litigation_cost/LitigationCostMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/vw_car_order_examine/VwCarOrderExamineMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/vw_car_order_examine/VwCarOrderExamineMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/vw_litigation_case_full/VwLitigationCaseFullMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/vw_litigation_case_full/VwLitigationCaseFullMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-common/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-common/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ruoyi-system/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-system/pom.xml" afterDir="false" />
    </list>
    <list id="289d0ba7-aa94-4810-8c8b-c7e6c8e88abb" name="qiuwe 进行的更改" comment="">
      <change beforePath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/loan_reminder/LoanReminderMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/ruoyi-admin/src/main/resources/mapper/loan_reminder/LoanReminderMapper.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\toolbox\maven\apache-maven-3.9.11" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zcEOEHomRwMjegLYAO7zFWSz4n" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;HTTP 请求.generated-requests | #2.executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi-common [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ruoyi-common [compile].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.RuoYiApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/code_project/java_project/loan/post-loan-backend-master&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.settings.deploy.options&quot;,
    &quot;settings.editor.splitter.proportion&quot;: &quot;0.3058104&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\code\project\java_project\post-loan-backend-master\ruoyi-framework\src\main\java\com\ruoyi\framework" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.RuoYiApplication">
    <configuration name="generated-requests | #2" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#2" index="2" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="openjdk-20" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="ruoyi-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.RuoYiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="HTTP 请求.generated-requests | #2" />
      <item itemvalue="Spring Boot.RuoYiApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP 请求.generated-requests | #2" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="402e787b-5396-48de-93d1-448d61bdbbd4" name="更改" comment="" />
      <created>1752021927182</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752021927182</updated>
      <workItem from="1752021928511" duration="1796000" />
      <workItem from="1752036883877" duration="14150000" />
      <workItem from="1752057902985" duration="5411000" />
      <workItem from="1752111273582" duration="14059000" />
      <workItem from="1752194023202" duration="4017000" />
      <workItem from="1752202448938" duration="9448000" />
      <workItem from="1752281681552" duration="2600000" />
      <workItem from="1752453466605" duration="7208000" />
      <workItem from="1752539641497" duration="1045000" />
      <workItem from="1752542325724" duration="284000" />
      <workItem from="1752542622883" duration="1362000" />
      <workItem from="1752544010274" duration="489000" />
      <workItem from="1752544513804" duration="711000" />
      <workItem from="1752545287164" duration="3329000" />
      <workItem from="1752626294917" duration="4365000" />
      <workItem from="1752648848974" duration="625000" />
      <workItem from="1752650862110" duration="1169000" />
      <workItem from="1752652082157" duration="1307000" />
      <workItem from="1752653696872" duration="3284000" />
      <workItem from="1752712503502" duration="4641000" />
      <workItem from="1752760510271" duration="693000" />
      <workItem from="1752895831983" duration="2499000" />
      <workItem from="1753150740355" duration="81000" />
      <workItem from="1753150854672" duration="2829000" />
      <workItem from="1753192951769" duration="65000" />
      <workItem from="1753231318569" duration="6915000" />
      <workItem from="1753319375859" duration="9609000" />
      <workItem from="1753851506899" duration="826000" />
      <workItem from="1753852345924" duration="1608000" />
      <workItem from="1753854828489" duration="1760000" />
      <workItem from="1753856762139" duration="613000" />
      <workItem from="1753857505857" duration="19000" />
      <workItem from="1753861755624" duration="663000" />
      <workItem from="1753928489710" duration="541000" />
    </task>
    <task id="LOCAL-00001" summary="2025-07-15">
      <option name="closed" value="true" />
      <created>1752544837364</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752544837364</updated>
    </task>
    <task id="LOCAL-00002" summary="还款状态管理">
      <option name="closed" value="true" />
      <created>1752586867652</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752586867652</updated>
    </task>
    <task id="LOCAL-00003" summary="合并代码">
      <option name="closed" value="true" />
      <created>1752661619741</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752661619741</updated>
    </task>
    <task id="LOCAL-00004" summary="合并代码">
      <option name="closed" value="true" />
      <created>1752895919406</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752895919406</updated>
    </task>
    <task id="LOCAL-00005" summary="合并代码2">
      <option name="closed" value="true" />
      <created>1752895997910</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752895997910</updated>
    </task>
    <task id="LOCAL-00006" summary="合并代码3">
      <option name="closed" value="true" />
      <created>1753152391294</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753152391295</updated>
    </task>
    <task id="LOCAL-00007" summary="合并代码3">
      <option name="closed" value="true" />
      <created>1753193005874</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753193005874</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="2025-07-15" />
    <MESSAGE value="还款状态管理" />
    <MESSAGE value="合并代码" />
    <MESSAGE value="合并代码2" />
    <MESSAGE value="合并代码3" />
    <option name="LAST_COMMIT_MESSAGE" value="合并代码3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>