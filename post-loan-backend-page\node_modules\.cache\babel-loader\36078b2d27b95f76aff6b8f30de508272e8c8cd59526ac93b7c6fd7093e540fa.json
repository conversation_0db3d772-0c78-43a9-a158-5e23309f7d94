{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addDaily_expense_approval = addDaily_expense_approval;\nexports.approveDaily_expense_approval = approveDaily_expense_approval;\nexports.delDaily_expense_approval = delDaily_expense_approval;\nexports.getDaily_expense_approval = getDaily_expense_approval;\nexports.listDaily_expense_approval = listDaily_expense_approval;\nexports.listPendingDaily_expense_approval = listPendingDaily_expense_approval;\nexports.updateDaily_expense_approval = updateDaily_expense_approval;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 查询日常花费审批列表\nfunction listDaily_expense_approval(query) {\n  return (0, _request.default)({\n    url: '/daily_expense_approval/daily_expense_approval/list',\n    method: 'get',\n    params: query\n  });\n}\n\n// 查询待审批的日常花费申请列表\nfunction listPendingDaily_expense_approval(query) {\n  return (0, _request.default)({\n    url: '/daily_expense_approval/daily_expense_approval/pendingList',\n    method: 'get',\n    params: query\n  });\n}\n\n// 查询日常花费审批详细\nfunction getDaily_expense_approval(id) {\n  return (0, _request.default)({\n    url: '/daily_expense_approval/daily_expense_approval/' + id,\n    method: 'get'\n  });\n}\n\n// 新增日常花费审批\nfunction addDaily_expense_approval(data) {\n  return (0, _request.default)({\n    url: '/daily_expense_approval/daily_expense_approval',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改日常花费审批\nfunction updateDaily_expense_approval(data) {\n  return (0, _request.default)({\n    url: '/daily_expense_approval/daily_expense_approval',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除日常花费审批\nfunction delDaily_expense_approval(id) {\n  return (0, _request.default)({\n    url: '/daily_expense_approval/daily_expense_approval/' + id,\n    method: 'delete'\n  });\n}\n\n// 审批日常花费申请\nfunction approveDaily_expense_approval(id, data) {\n  return (0, _request.default)({\n    url: '/daily_expense_approval/daily_expense_approval/approve/' + id,\n    method: 'post',\n    data: data\n  });\n}", "map": {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDaily_expense_approval", "query", "request", "url", "method", "params", "listPendingDaily_expense_approval", "getDaily_expense_approval", "id", "addDaily_expense_approval", "data", "updateDaily_expense_approval", "delDaily_expense_approval", "approveDaily_expense_approval"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/api/daily_expense_approval/daily_expense_approval.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询日常花费审批列表\r\nexport function listDaily_expense_approval(query) {\r\n  return request({\r\n    url: '/daily_expense_approval/daily_expense_approval/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询待审批的日常花费申请列表\r\nexport function listPendingDaily_expense_approval(query) {\r\n  return request({\r\n    url: '/daily_expense_approval/daily_expense_approval/pendingList',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询日常花费审批详细\r\nexport function getDaily_expense_approval(id) {\r\n  return request({\r\n    url: '/daily_expense_approval/daily_expense_approval/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增日常花费审批\r\nexport function addDaily_expense_approval(data) {\r\n  return request({\r\n    url: '/daily_expense_approval/daily_expense_approval',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改日常花费审批\r\nexport function updateDaily_expense_approval(data) {\r\n  return request({\r\n    url: '/daily_expense_approval/daily_expense_approval',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除日常花费审批\r\nexport function delDaily_expense_approval(id) {\r\n  return request({\r\n    url: '/daily_expense_approval/daily_expense_approval/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 审批日常花费申请\r\nexport function approveDaily_expense_approval(id, data) {\r\n  return request({\r\n    url: '/daily_expense_approval/daily_expense_approval/approve/' + id,\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,0BAA0BA,CAACC,KAAK,EAAE;EAChD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qDAAqD;IAC1DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iCAAiCA,CAACL,KAAK,EAAE;EACvD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4DAA4D;IACjEC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,yBAAyBA,CAACC,EAAE,EAAE;EAC5C,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,iDAAiD,GAAGK,EAAE;IAC3DJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,gDAAgD;IACrDC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,4BAA4BA,CAACD,IAAI,EAAE;EACjD,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,gDAAgD;IACrDC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,yBAAyBA,CAACJ,EAAE,EAAE;EAC5C,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,iDAAiD,GAAGK,EAAE;IAC3DJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,6BAA6BA,CAACL,EAAE,EAAEE,IAAI,EAAE;EACtD,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,yDAAyD,GAAGK,EAAE;IACnEJ,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}