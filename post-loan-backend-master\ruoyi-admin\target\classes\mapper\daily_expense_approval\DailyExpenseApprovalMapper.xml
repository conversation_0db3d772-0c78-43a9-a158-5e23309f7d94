<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.daily_expense_approval.mapper.DailyExpenseApprovalMapper">
    
    <resultMap type="DailyExpenseApproval" id="DailyExpenseApprovalResult">
        <result property="id"    column="id"    />
        <result property="litigationCaseId"    column="litigation_case_id"    />
        <result property="expenseType"    column="expense_type"    />
        <result property="expenseAmount"    column="expense_amount"    />
        <result property="expenseDate"    column="expense_date"    />
        <result property="expenseDescription"    column="expense_description"    />
        <result property="receiptUrl"    column="receipt_url"    />
        <result property="applicantId"    column="applicant_id"    />
        <result property="applicantName"    column="applicant_name"    />
        <result property="applicationTime"    column="application_time"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="approverId"    column="approver_id"    />
        <result property="approverName"    column="approver_name"    />
        <result property="approvalTime"    column="approval_time"    />
        <result property="approvalRemark"    column="approval_remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDailyExpenseApprovalVo">
        select id, litigation_case_id, expense_type, expense_amount, expense_date, expense_description, receipt_url, applicant_id, applicant_name, application_time, approval_status, approver_id, approver_name, approval_time, approval_remark, create_by, create_time, update_by, update_time, del_flag from daily_expense_approval
    </sql>

    <select id="selectDailyExpenseApprovalList" parameterType="DailyExpenseApproval" resultMap="DailyExpenseApprovalResult">
        <include refid="selectDailyExpenseApprovalVo"/>
        <where>  
            <if test="litigationCaseId != null "> and litigation_case_id = #{litigationCaseId}</if>
            <if test="expenseType != null  and expenseType != ''"> and expense_type = #{expenseType}</if>
            <if test="expenseAmount != null "> and expense_amount = #{expenseAmount}</if>
            <if test="expenseDate != null "> and expense_date = #{expenseDate}</if>
            <if test="expenseDescription != null  and expenseDescription != ''"> and expense_description = #{expenseDescription}</if>
            <if test="receiptUrl != null  and receiptUrl != ''"> and receipt_url = #{receiptUrl}</if>
            <if test="applicantId != null  and applicantId != ''"> and applicant_id = #{applicantId}</if>
            <if test="applicantName != null  and applicantName != ''"> and applicant_name like concat('%', #{applicantName}, '%')</if>
            <if test="applicationTime != null "> and application_time = #{applicationTime}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="approverId != null  and approverId != ''"> and approver_id = #{approverId}</if>
            <if test="approverName != null  and approverName != ''"> and approver_name like concat('%', #{approverName}, '%')</if>
            <if test="approvalStartTime != null and approvalStartTime != ''"> and date(approval_time) &gt;= #{approvalStartTime}</if>
            <if test="approvalEndTime != null and approvalEndTime != ''"> and date(approval_time) &lt;= #{approvalEndTime}</if>
            <if test="approvalRemark != null  and approvalRemark != ''"> and approval_remark = #{approvalRemark}</if>
        </where>
    </select>
    
    <select id="selectDailyExpenseApprovalById" parameterType="Long" resultMap="DailyExpenseApprovalResult">
        <include refid="selectDailyExpenseApprovalVo"/>
        where id = #{id}
    </select>

    <insert id="insertDailyExpenseApproval" parameterType="DailyExpenseApproval" useGeneratedKeys="true" keyProperty="id">
        insert into daily_expense_approval
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="litigationCaseId != null">litigation_case_id,</if>
            <if test="expenseType != null and expenseType != ''">expense_type,</if>
            <if test="expenseAmount != null">expense_amount,</if>
            <if test="expenseDate != null">expense_date,</if>
            <if test="expenseDescription != null">expense_description,</if>
            <if test="receiptUrl != null">receipt_url,</if>
            <if test="applicantId != null and applicantId != ''">applicant_id,</if>
            <if test="applicantName != null and applicantName != ''">applicant_name,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status,</if>
            <if test="approverId != null">approver_id,</if>
            <if test="approverName != null">approver_name,</if>
            <if test="approvalTime != null">approval_time,</if>
            <if test="approvalRemark != null">approval_remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="litigationCaseId != null">#{litigationCaseId},</if>
            <if test="expenseType != null and expenseType != ''">#{expenseType},</if>
            <if test="expenseAmount != null">#{expenseAmount},</if>
            <if test="expenseDate != null">#{expenseDate},</if>
            <if test="expenseDescription != null">#{expenseDescription},</if>
            <if test="receiptUrl != null">#{receiptUrl},</if>
            <if test="applicantId != null and applicantId != ''">#{applicantId},</if>
            <if test="applicantName != null and applicantName != ''">#{applicantName},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="approvalStatus != null and approvalStatus != ''">#{approvalStatus},</if>
            <if test="approverId != null">#{approverId},</if>
            <if test="approverName != null">#{approverName},</if>
            <if test="approvalTime != null">#{approvalTime},</if>
            <if test="approvalRemark != null">#{approvalRemark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateDailyExpenseApproval" parameterType="DailyExpenseApproval">
        update daily_expense_approval
        <trim prefix="SET" suffixOverrides=",">
            <if test="litigationCaseId != null">litigation_case_id = #{litigationCaseId},</if>
            <if test="expenseType != null and expenseType != ''">expense_type = #{expenseType},</if>
            <if test="expenseAmount != null">expense_amount = #{expenseAmount},</if>
            <if test="expenseDate != null">expense_date = #{expenseDate},</if>
            <if test="expenseDescription != null">expense_description = #{expenseDescription},</if>
            <if test="receiptUrl != null">receipt_url = #{receiptUrl},</if>
            <if test="applicantId != null and applicantId != ''">applicant_id = #{applicantId},</if>
            <if test="applicantName != null and applicantName != ''">applicant_name = #{applicantName},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="approverId != null">approver_id = #{approverId},</if>
            <if test="approverName != null">approver_name = #{approverName},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="approvalRemark != null">approval_remark = #{approvalRemark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDailyExpenseApprovalById" parameterType="Long">
        delete from daily_expense_approval where id = #{id}
    </delete>

    <delete id="deleteDailyExpenseApprovalByIds" parameterType="String">
        delete from daily_expense_approval where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateNullApprovalStatus">
        update daily_expense_approval
        set approval_status = '0'
        where approval_status is null
    </update>
</mapper>