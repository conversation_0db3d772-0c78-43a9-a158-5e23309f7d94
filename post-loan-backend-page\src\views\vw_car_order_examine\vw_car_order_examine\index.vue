<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="" prop="customerName">
        <el-input v-model="queryParams.customerName" placeholder="贷款人账户、姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="plateNo">
        <el-input v-model="queryParams.plateNo" placeholder="请输入车牌号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="jgName">
        <el-input v-model="queryParams.jgName" placeholder="录入渠道名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="garageName">
        <el-input v-model="queryParams.garageName" placeholder="请输入车库名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="" prop="keyStatus">
        <el-select v-model="queryParams.keyStatus" placeholder="请选择钥匙状态" clearable>
          <el-option v-for="dict in keyStatusList" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="teamName">
        <el-input v-model="queryParams.teamName" placeholder="找车团队" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="派单时间">
        <el-date-picker
          v-model="queryParams.originallyTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="vw_car_order_examineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="55" fixed="left" />
      <el-table-column label="贷款人" align="center" prop="customerName">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.customerId && scope.row.applyId"
            type="text"
            @click="openUserInfo({ customerId: scope.row.customerId, applyId: scope.row.applyId })">
            {{ scope.row.customerName }}
          </el-button>
          <span v-else>{{ scope.row.customerName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="mobilePhone" />
      <!-- 出单渠道 -->
      <el-table-column label="出单渠道" align="center" prop="jgName"></el-table-column>
      <el-table-column label="车牌号" align="center" prop="plateNo">
        <template slot-scope="scope">
          <el-button type="text" @click="openCarInfo(scope.row.plateNo)">{{ scope.row.plateNo }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="接单团队" align="center" prop="teamName" />
      <el-table-column label="派单时间" align="center" prop="allocationTime" width="180"></el-table-column>
      <el-table-column label="钥匙状态" align="center" prop="keyStatus">
        <template slot-scope="scope">
          <span>{{ scope.row.keyStatus == 1 ? '已邮寄' : scope.row.keyStatus == 2 ? '已收回' : '未归还' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入库时间" align="center" prop="inboundTime" width="180"></el-table-column>
      <el-table-column label="找车佣金" align="center" prop="locatingCommission" />
      <el-table-column label="佣金" align="center" prop="transportationFee" />
      <el-table-column label="拖车费" align="center" prop="towingFee" />
      <el-table-column label="贴机费" align="center" prop="trackerInstallationFee" />
      <el-table-column label="其他报销" align="center" prop="otherReimbursement" />
      <el-table-column label="合计费用" align="center" prop="totalCost" />
      <el-table-column label="审批状态" align="center" prop="status">
        <template slot-scope="scope">
          <span>{{ getStatusText(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="canApprove(scope.row)"
            size="mini"
            type="text"
            @click="handleExamine(scope.row)"
            v-hasPermi="['vw_car_order_examine:vw_car_order_examine:edit']">
            审批
          </el-button>
          <el-button
            v-if="scope.row.status == 1 || scope.row.status == 7"
            size="mini"
            type="text"
            @click="handleExamine(scope.row, true)"
            v-hasPermi="['vw_car_order_examine:vw_car_order_examine:edit']">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改找车费用审批对话框 -->
    <el-dialog :title="form._readonly ? '详情' : '审批'" :visible.sync="open" width="500px" append-to-body>
      <el-descriptions :column="2" border style="margin-bottom: 20px">
        <el-descriptions-item label="贷款人">
          <el-button
            v-if="form.customerId && form.applyId"
            type="text"
            @click="openUserInfo({ customerId: form.customerId, applyId: form.applyId })">
            {{ form.customerName }}
          </el-button>
          <span v-else>{{ form.customerName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="车牌号">
          <el-button type="text" @click="openCarInfo(form.plateNo)">{{ form.plateNo }}</el-button>
        </el-descriptions-item>
        <el-descriptions-item label="出单渠道">{{ form.jgName }}</el-descriptions-item>
        <el-descriptions-item label="接单团队">{{ form.teamName }}</el-descriptions-item>
        <el-descriptions-item label="派单时间">{{ form.allocationTime }}</el-descriptions-item>
        <el-descriptions-item label="钥匙状态">
          <span>{{ form.keyStatus == 1 ? '已邮寄' : form.keyStatus == 2 ? '已收回' : '未归还' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="合计费用">{{ form.totalCost }}</el-descriptions-item>
      </el-descriptions>
      <el-form ref="form" :model="form" label-width="100px">
        <template v-if="form._readonly && form.status === 1">
          <div style="color: #67c23a; font-size: 18px; margin-bottom: 16px;">全部通过</div>
        </template>
        <template v-else-if="form._readonly && form.status === 7">
          <div style="color: #f56c6c; font-size: 18px; margin-bottom: 16px;">已拒绝</div>
        </template>
        <template v-else-if="form._readonly">
          <div style="color: #409eff; font-size: 18px; margin-bottom: 16px;">{{ getStatusText(form.status) }}</div>
        </template>
        <template v-if="!form._readonly">
          <el-form-item label="当前状态">
            <span>{{ getStatusText(form.status) }}</span>
          </el-form-item>
          <el-form-item label="审批操作">
            <el-radio-group v-model="form.newStatus">
              <el-radio :label="getNextStatus(form.status)">通过</el-radio>
              <el-radio :label="7">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="form.newStatus == 7" label="拒绝原因">
            <el-input type="textarea" :rows="2" placeholder="请输入拒绝原因" v-model="form.rejectReason"></el-input>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="!form._readonly" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">{{ form._readonly ? '关 闭' : '取 消' }}</el-button>
      </div>
    </el-dialog>
    <!-- 贷款人信息组件 -->
    <userInfo ref="userInfo" :visible.sync="userInfoVisible" title="贷款人信息" :customerInfo="customerInfo" />
    <!-- 车辆信息组件 -->
    <carInfo ref="carInfo" :visible.sync="carInfoVisible" title="车辆信息" :plateNo="plateNo" permission="2" />
  </div>
</template>

<script>
import {
  teamVm_car_order,
  examine_order,
  approveCarOrderExamine,
  listPendingApproval,
  listVw_car_order_examine,
  getVw_car_order_examine,
  delVw_car_order_examine,
  addVw_car_order_examine,
  updateVw_car_order_examine,
} from '@/api/vw_car_order_examine/vw_car_order_examine'
import userInfo from '@/layout/components/Dialog/userInfo.vue'
import carInfo from '@/layout/components/Dialog/carInfo.vue'

export default {
  name: 'Vw_car_order_examine',
  components: {
    userInfo,
    carInfo,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 找车费用审批表格数据
      vw_car_order_examineList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 15,
        teamName: null,
        keyStatus: null,
        originallyTime: null,
        startTime: '',
        endTime: '',
        customerName: null,
        plateNo: null,
        jgName: null,
      },
      // 表单参数
      form: {
        id: '',
        status: 0,
        newStatus: null,
        rejectReason: null,
        customerName: '',
        customerId: '',
        applyId: '',
        plateNo: '',
        jgName: '',
        teamName: '',
        allocationTime: '',
        keyStatus: '',
        totalCost: '',
        _readonly: false,
      },
      // 表单校验
      rules: {
        id: [{ required: true, message: '$comment不能为空', trigger: 'blur' }],
      },
      jgNameList: [
        { label: 'A公司', value: 1 },
        { label: 'B公司', value: 2 },
      ],
      keyStatusList: [
        { label: '已邮寄', value: 1 },
        { label: '已收回', value: 2 },
        { label: '已归还', value: 3 },
      ],
      teamList: [
        { label: 'A团队', value: 1 },
        { label: 'B团队', value: 2 },
      ],
      customerInfo: { customerId: '', applyId: '' },
      userInfoVisible: false,
      plateNo: '',
      carInfoVisible: false,
    }
  },
  created() {
    this.getTeam()
    this.getList()
  },
  methods: {
    // 查询录单渠道、找车团队
    getTeam() {
      teamVm_car_order().then(response => {
        this.teamList = response.team
        this.jgNameList = response.office
      })
    },
    /** 查询找车费用审批列表 */
    getList() {
      this.loading = true
      listVw_car_order_examine(this.queryParams).then(response => {
        this.vw_car_order_examineList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        status: 0,
        newStatus: null,
        rejectReason: null,
        customerName: '',
        customerId: '',
        applyId: '',
        plateNo: '',
        jgName: '',
        teamName: '',
        allocationTime: '',
        keyStatus: '',
        totalCost: '',
        _readonly: false,
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.originallyTime) {
        this.queryParams.startTime = this.queryParams.originallyTime[0]
        this.queryParams.endTime = this.queryParams.originallyTime[1]
      }
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.customerName = null
      this.queryParams.plateNo = null
      this.queryParams.jgName = null
      this.queryParams.keyStatus = null
      this.queryParams.teamName = null
      this.queryParams.originallyTime = null
      this.queryParams.startTime = null
      this.queryParams.endTime = null
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加找车费用审批'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // this.reset()
      this.form.id = row.id
      this.form.rejectReason = row.rejectReason
      this.form.status = row.status
      // const id = row.id || this.ids
      // getVw_car_order_examine(id).then(response => {
      //   // this.form = response.data

      // })
      this.open = true
      this.title = '找车费用审批'
    },
    handleExamine(row, isDetail = false) {
      this.form = {
        id: row.id,
        status: row.status || 0,
        newStatus: null,
        rejectReason: row.rejectReason || null,
        customerName: row.customerName || '',
        customerId: row.customerId || '',
        applyId: row.applyId || '',
        plateNo: row.plateNo || '',
        jgName: row.jgName || row.channel || '',
        teamName: row.teamName || '',
        allocationTime: row.allocationTime || '',
        keyStatus: row.keyStatus || '',
        totalCost: row.totalCost || '',
        _readonly: !!isDetail,
      }
      this.open = true
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '未审批',
        1: '全部通过',
        3: '法诉主管审批',
        4: '总监审批',
        5: '总监抄送',
        6: '总经理/董事长审批(抄送)',
        7: '已拒绝'
      }
      return statusMap[status] || '未知状态'
    },
    // 获取下一个状态
    getNextStatus(currentStatus) {
      const nextStatusMap = {
        0: 3,  // 未审批 -> 法诉主管审批
        3: 4,  // 法诉主管审批 -> 总监审批
        4: 5,  // 总监审批 -> 总监抄送
        5: 6,  // 总监抄送 -> 总经理/董事长审批(抄送)
        6: 1   // 总经理/董事长审批(抄送) -> 全部通过
      }
      return nextStatusMap[currentStatus] || currentStatus
    },
    // 判断是否可以审批
    canApprove(row) {
      // 只有未完成的状态才能审批
      return row.status !== 1 && row.status !== 7
    },
    submitForm() {
      if (!this.form.newStatus) {
        this.$modal.msgError('请选择审批结果')
        return
      }
      if (this.form.newStatus == 7 && !this.form.rejectReason) {
        this.$modal.msgError('请输入拒绝原因')
        return
      }

      // 准备提交数据
      const submitData = {
        id: this.form.id,
        status: this.form.newStatus,
        rejectReason: this.form.rejectReason
      }

      // 使用新的审批流程接口
      approveCarOrderExamine(submitData).then(() => {
        this.$modal.msgSuccess('审批成功')
        this.open = false
        this.getList()
      }).catch(error => {
        this.$modal.msgError(error.msg || '审批失败')
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除找车费用审批编号为"' + ids + '"的数据项？')
        .then(function () {
          return delVw_car_order_examine(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'vw_car_order_examine/vw_car_order_examine/export',
        {
          ...this.queryParams,
        },
        `vw_car_order_examine_${new Date().getTime()}.xlsx`
      )
    },
    openUserInfo(customerInfo) {
      console.log('点击客户信息:', customerInfo)
      if (!customerInfo.customerId || !customerInfo.applyId) {
        this.$modal.msgError('客户信息不完整，无法查看详情')
        return
      }
      this.customerInfo = customerInfo
      this.userInfoVisible = true
    },
    openCarInfo(plateNo) {
      this.plateNo = plateNo
      this.carInfoVisible = true
    },
  },
}
</script>
