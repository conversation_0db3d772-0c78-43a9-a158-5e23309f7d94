{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nvar _daily_expense_approval = require(\"@/api/daily_expense_approval/daily_expense_approval\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"Daily_expense_approval\",\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 日常花费审批表格数据\n      daily_expense_approvalList: [],\n      // 查看详情弹窗\n      viewDialogVisible: false,\n      viewData: {},\n      // 审批时间范围\n      approvalTimeRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        litigationCaseId: null,\n        expenseType: null,\n        expenseAmount: null,\n        expenseDate: null,\n        expenseDescription: null,\n        receiptUrl: null,\n        applicantId: null,\n        applicantName: null,\n        applicationTime: null,\n        approvalStatus: null,\n        approverId: null,\n        approverName: null,\n        approvalStartTime: null,\n        approvalEndTime: null,\n        approvalRemark: null\n      }\n    };\n  },\n  created: function created() {\n    this.getList();\n    // 检查是否从法诉页面跳转过来\n    if (this.$route.query.litigationCaseId) {\n      this.queryParams.litigationCaseId = this.$route.query.litigationCaseId;\n      this.handleQuery();\n    }\n  },\n  methods: {\n    /** 查询日常花费审批列表 */getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _daily_expense_approval.listDaily_expense_approval)(this.queryParams).then(function (response) {\n        _this.daily_expense_approvalList = response.rows;\n        _this.total = response.total;\n        _this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.approvalTimeRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 处理审批时间范围变化 */handleApprovalTimeRangeChange: function handleApprovalTimeRangeChange(dates) {\n      if (dates && dates.length === 2) {\n        this.queryParams.approvalStartTime = dates[0];\n        this.queryParams.approvalEndTime = dates[1];\n      } else {\n        this.queryParams.approvalStartTime = null;\n        this.queryParams.approvalEndTime = null;\n      }\n      this.handleQuery();\n    },\n    /** 导出按钮操作 */handleExport: function handleExport() {\n      this.download('daily_expense_approval/daily_expense_approval/export', (0, _objectSpread2.default)({}, this.queryParams), \"daily_expense_approval_\".concat(new Date().getTime(), \".xlsx\"));\n    },\n    // 表单重置\n    reset: function reset() {\n      this.form = {\n        id: null,\n        litigationCaseId: null,\n        expenseType: null,\n        expenseAmount: null,\n        expenseDate: null,\n        expenseDescription: null,\n        receiptUrl: null,\n        applicantId: null,\n        applicantName: null,\n        applicationTime: null,\n        approvalStatus: '0',\n        // 默认待审批\n        approverId: null,\n        approverName: null,\n        approvalTime: null,\n        approvalRemark: null,\n        delFlag: '0' // 默认未删除\n      };\n      this.resetForm(\"form\");\n    },\n    /** 审批操作 */handleApprove: function handleApprove(row, action) {\n      var _this2 = this;\n      var statusText = action === 'approve' ? '通过' : '拒绝';\n      if (action === 'approve') {\n        // 通过审批，直接确认\n        this.$confirm(\"\\u786E\\u8BA4\".concat(statusText, \"\\u8BE5\\u65E5\\u5E38\\u8D39\\u7528\\u7533\\u8BF7\\uFF1F\"), \"\".concat(statusText, \"\\u5BA1\\u6279\"), {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(function () {\n          var approvalData = {\n            action: action,\n            remark: '审批通过'\n          };\n          (0, _daily_expense_approval.approveDaily_expense_approval)(row.id, approvalData).then(function (response) {\n            _this2.$modal.msgSuccess(\"\".concat(statusText, \"\\u6210\\u529F\"));\n            _this2.getList();\n          }).catch(function (error) {\n            _this2.$modal.msgError(\"\".concat(statusText, \"\\u5931\\u8D25\\uFF1A\") + (error.msg || '未知错误'));\n          });\n        }).catch(function () {\n          _this2.$modal.msgInfo('已取消审批');\n        });\n      } else {\n        // 拒绝审批，需要填写理由\n        this.$prompt(\"\\u8BF7\\u8F93\\u5165\".concat(statusText, \"\\u7406\\u7531\"), \"\".concat(statusText, \"\\u5BA1\\u6279\"), {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          inputPattern: /.+/,\n          inputErrorMessage: '请输入审批理由'\n        }).then(function (_ref) {\n          var value = _ref.value;\n          var approvalData = {\n            action: action,\n            remark: value\n          };\n          (0, _daily_expense_approval.approveDaily_expense_approval)(row.id, approvalData).then(function (response) {\n            _this2.$modal.msgSuccess(\"\".concat(statusText, \"\\u6210\\u529F\"));\n            _this2.getList();\n          }).catch(function (error) {\n            _this2.$modal.msgError(\"\".concat(statusText, \"\\u5931\\u8D25\\uFF1A\") + (error.msg || '未知错误'));\n          });\n        }).catch(function () {\n          _this2.$modal.msgInfo('已取消审批');\n        });\n      }\n    },\n    /** 查看详情 */handleView: function handleView(row) {\n      this.viewData = (0, _objectSpread2.default)({}, row);\n      this.viewDialogVisible = true;\n    },\n    /** 获取费用类型标签 */getExpenseTypeLabel: function getExpenseTypeLabel(type) {\n      var typeMap = {\n        'oil_fee': '油费',\n        'road_fee': '路费',\n        'meal_fee': '餐费',\n        'accommodation_fee': '住宿费',\n        'transport_fee': '交通费',\n        'parking_fee': '停车费',\n        'communication_fee': '通讯费',\n        'other': '其他'\n      };\n      return typeMap[type] || type;\n    },\n    /** 获取审批状态文本 */getStatusText: function getStatusText(status) {\n      var statusMap = {\n        '0': '待审批',\n        '1': '全部通过',\n        '2': '已拒绝',\n        '3': '主管审批中',\n        '4': '总监审批中',\n        '5': '财务主管审批中',\n        '6': '总经理审批中'\n      };\n      return statusMap[status] || '未知状态';\n    },\n    /** 获取审批状态标签类型 */getStatusTagType: function getStatusTagType(status) {\n      var typeMap = {\n        '0': 'warning',\n        // 待审批 - 橙色\n        '1': 'success',\n        // 全部通过 - 绿色\n        '2': 'danger',\n        // 已拒绝 - 红色\n        '3': 'primary',\n        // 主管审批中 - 蓝色\n        '4': 'primary',\n        // 总监审批中 - 蓝色\n        '5': 'primary',\n        // 财务主管审批中 - 蓝色\n        '6': 'primary' // 总经理审批中 - 蓝色\n      };\n      return typeMap[status] || 'info';\n    },\n    /** 判断是否可以审批 */canApprove: function canApprove(row) {\n      // 已完成的状态不能审批\n      if (row.approvalStatus === '1' || row.approvalStatus === '2') {\n        return false;\n      }\n\n      // 这里可以根据当前用户角色和审批状态来判断\n      // 简化处理：只要不是已完成状态就可以审批\n      // 实际权限控制在后端进行\n      return true;\n    }\n  }\n};", "map": {"version": 3, "names": ["_daily_expense_approval", "require", "name", "data", "loading", "showSearch", "total", "daily_expense_approvalList", "viewDialogVisible", "viewData", "approvalTimeRange", "queryParams", "pageNum", "pageSize", "litigationCaseId", "expenseType", "expenseAmount", "expenseDate", "expenseDescription", "receiptUrl", "applicantId", "applicantName", "applicationTime", "approvalStatus", "approverId", "approver<PERSON><PERSON>", "approvalStartTime", "approvalEndTime", "approvalRemark", "created", "getList", "$route", "query", "handleQuery", "methods", "_this", "listDaily_expense_approval", "then", "response", "rows", "reset<PERSON><PERSON>y", "resetForm", "handleApprovalTimeRangeChange", "dates", "length", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "reset", "form", "id", "approvalTime", "delFlag", "handleApprove", "row", "action", "_this2", "statusText", "$confirm", "confirmButtonText", "cancelButtonText", "type", "approvalData", "remark", "approveDaily_expense_approval", "$modal", "msgSuccess", "catch", "error", "msgError", "msg", "msgInfo", "$prompt", "inputPattern", "inputErrorMessage", "_ref", "value", "handleView", "getExpenseTypeLabel", "typeMap", "getStatusText", "status", "statusMap", "getStatusTagType", "canApprove"], "sources": ["src/views/daily_expense_approval/daily_expense_approval/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"expenseAmount\">\r\n        <el-input\r\n          v-model=\"queryParams.expenseAmount\"\r\n          placeholder=\"请输入费用金额\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"expenseDate\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.expenseDate\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择费用发生日期\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"applicantId\">\r\n        <el-input\r\n          v-model=\"queryParams.applicantId\"\r\n          placeholder=\"请输入申请人ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"applicantName\">\r\n        <el-input\r\n          v-model=\"queryParams.applicantName\"\r\n          placeholder=\"请输入申请人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"applicationTime\">\r\n        <el-date-picker clearable\r\n          v-model=\"queryParams.applicationTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择申请时间\">\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"approverId\">\r\n        <el-input\r\n          v-model=\"queryParams.approverId\"\r\n          placeholder=\"请输入审批人ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"approverName\">\r\n        <el-input\r\n          v-model=\"queryParams.approverName\"\r\n          placeholder=\"请输入审批人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"approvalTimeRange\">\r\n        <el-date-picker\r\n          v-model=\"approvalTimeRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleApprovalTimeRangeChange\"\r\n          clearable>\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button\r\n          :type=\"showPendingOnly ? 'success' : 'info'\"\r\n          icon=\"el-icon-s-check\"\r\n          size=\"mini\"\r\n          @click=\"togglePendingView\">\r\n          {{ showPendingOnly ? '显示全部' : '待我审批' }}\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['daily_expense_approval:daily_expense_approval:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"daily_expense_approvalList\">\r\n      <el-table-column label=\"费用类型\" align=\"center\" prop=\"expenseType\" />\r\n      <el-table-column label=\"费用金额\" align=\"center\" prop=\"expenseAmount\" />\r\n      <el-table-column label=\"费用发生日期\" align=\"center\" prop=\"expenseDate\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.expenseDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"费用说明\" align=\"center\" prop=\"expenseDescription\" />\r\n      <el-table-column label=\"申请人姓名\" align=\"center\" prop=\"applicantName\" />\r\n      <el-table-column label=\"申请时间\" align=\"center\" prop=\"applicationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"getStatusTagType(scope.row.approvalStatus)\">\r\n            {{ getStatusText(scope.row.approvalStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批人\" align=\"center\" prop=\"approverName\" />\r\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"approvalTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.approvalTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批备注\" align=\"center\" prop=\"approvalRemark\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"canApprove(scope.row)\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleApprove(scope.row, 'approve')\"\r\n            v-hasPermi=\"['daily_expense_approval:daily_expense_approval:edit']\"\r\n          >通过</el-button>\r\n          <el-button\r\n            v-if=\"canApprove(scope.row)\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-close\"\r\n            @click=\"handleApprove(scope.row, 'reject')\"\r\n            v-hasPermi=\"['daily_expense_approval:daily_expense_approval:edit']\"\r\n          >拒绝</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n          >查看</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 查看详情弹窗 -->\r\n    <el-dialog title=\"日常费用详情\" :visible.sync=\"viewDialogVisible\" width=\"600px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"费用类型\">\r\n          {{ getExpenseTypeLabel(viewData.expenseType) }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"费用金额\">\r\n          ￥{{ viewData.expenseAmount }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"费用发生日期\">\r\n          {{ parseTime(viewData.expenseDate, '{y}-{m}-{d}') }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"申请人\">\r\n          {{ viewData.applicantName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"申请时间\">\r\n          {{ parseTime(viewData.applicationTime, '{y}-{m}-{d}') }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审批状态\">\r\n          <el-tag :type=\"getStatusTagType(viewData.approvalStatus)\">\r\n            {{ getStatusText(viewData.approvalStatus) }}\r\n          </el-tag>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审批人\" v-if=\"viewData.approvalStatus !== '0'\">\r\n          {{ viewData.approverName || '-' }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审批时间\" v-if=\"viewData.approvalStatus !== '0'\">\r\n          {{ viewData.approvalTime ? parseTime(viewData.approvalTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"费用说明\" :span=\"2\">\r\n          {{ viewData.expenseDescription || '-' }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"审批备注\" :span=\"2\" v-if=\"viewData.approvalStatus !== '0'\">\r\n          {{ viewData.approvalRemark || '-' }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"viewDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDaily_expense_approval, approveDaily_expense_approval } from \"@/api/daily_expense_approval/daily_expense_approval\"\r\n\r\nexport default {\r\n  name: \"Daily_expense_approval\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 日常花费审批表格数据\r\n      daily_expense_approvalList: [],\r\n      // 查看详情弹窗\r\n      viewDialogVisible: false,\r\n      viewData: {},\r\n      // 审批时间范围\r\n      approvalTimeRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        litigationCaseId: null,\r\n        expenseType: null,\r\n        expenseAmount: null,\r\n        expenseDate: null,\r\n        expenseDescription: null,\r\n        receiptUrl: null,\r\n        applicantId: null,\r\n        applicantName: null,\r\n        applicationTime: null,\r\n        approvalStatus: null,\r\n        approverId: null,\r\n        approverName: null,\r\n        approvalStartTime: null,\r\n        approvalEndTime: null,\r\n        approvalRemark: null,\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    // 检查是否从法诉页面跳转过来\r\n    if (this.$route.query.litigationCaseId) {\r\n      this.queryParams.litigationCaseId = this.$route.query.litigationCaseId\r\n      this.handleQuery()\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询日常花费审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listDaily_expense_approval(this.queryParams).then(response => {\r\n        this.daily_expense_approvalList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.approvalTimeRange = []\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 处理审批时间范围变化 */\r\n    handleApprovalTimeRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.approvalStartTime = dates[0]\r\n        this.queryParams.approvalEndTime = dates[1]\r\n      } else {\r\n        this.queryParams.approvalStartTime = null\r\n        this.queryParams.approvalEndTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('daily_expense_approval/daily_expense_approval/export', {\r\n        ...this.queryParams\r\n      }, `daily_expense_approval_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        litigationCaseId: null,\r\n        expenseType: null,\r\n        expenseAmount: null,\r\n        expenseDate: null,\r\n        expenseDescription: null,\r\n        receiptUrl: null,\r\n        applicantId: null,\r\n        applicantName: null,\r\n        applicationTime: null,\r\n        approvalStatus: '0', // 默认待审批\r\n        approverId: null,\r\n        approverName: null,\r\n        approvalTime: null,\r\n        approvalRemark: null,\r\n        delFlag: '0' // 默认未删除\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n\r\n    /** 审批操作 */\r\n    handleApprove(row, action) {\r\n      const statusText = action === 'approve' ? '通过' : '拒绝'\r\n\r\n      if (action === 'approve') {\r\n        // 通过审批，直接确认\r\n        this.$confirm(`确认${statusText}该日常费用申请？`, `${statusText}审批`, {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          const approvalData = {\r\n            action: action,\r\n            remark: '审批通过'\r\n          }\r\n\r\n          approveDaily_expense_approval(row.id, approvalData).then(response => {\r\n            this.$modal.msgSuccess(`${statusText}成功`)\r\n            this.getList()\r\n          }).catch(error => {\r\n            this.$modal.msgError(`${statusText}失败：` + (error.msg || '未知错误'))\r\n          })\r\n        }).catch(() => {\r\n          this.$modal.msgInfo('已取消审批')\r\n        })\r\n      } else {\r\n        // 拒绝审批，需要填写理由\r\n        this.$prompt(`请输入${statusText}理由`, `${statusText}审批`, {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          inputPattern: /.+/,\r\n          inputErrorMessage: '请输入审批理由'\r\n        }).then(({ value }) => {\r\n          const approvalData = {\r\n            action: action,\r\n            remark: value\r\n          }\r\n\r\n          approveDaily_expense_approval(row.id, approvalData).then(response => {\r\n            this.$modal.msgSuccess(`${statusText}成功`)\r\n            this.getList()\r\n          }).catch(error => {\r\n            this.$modal.msgError(`${statusText}失败：` + (error.msg || '未知错误'))\r\n          })\r\n        }).catch(() => {\r\n          this.$modal.msgInfo('已取消审批')\r\n        })\r\n      }\r\n    },\r\n\r\n    /** 查看详情 */\r\n    handleView(row) {\r\n      this.viewData = { ...row }\r\n      this.viewDialogVisible = true\r\n    },\r\n\r\n    /** 获取费用类型标签 */\r\n    getExpenseTypeLabel(type) {\r\n      const typeMap = {\r\n        'oil_fee': '油费',\r\n        'road_fee': '路费',\r\n        'meal_fee': '餐费',\r\n        'accommodation_fee': '住宿费',\r\n        'transport_fee': '交通费',\r\n        'parking_fee': '停车费',\r\n        'communication_fee': '通讯费',\r\n        'other': '其他'\r\n      }\r\n      return typeMap[type] || type\r\n    },\r\n\r\n    /** 获取审批状态文本 */\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        '0': '待审批',\r\n        '1': '全部通过',\r\n        '2': '已拒绝',\r\n        '3': '主管审批中',\r\n        '4': '总监审批中',\r\n        '5': '财务主管审批中',\r\n        '6': '总经理审批中'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    /** 获取审批状态标签类型 */\r\n    getStatusTagType(status) {\r\n      const typeMap = {\r\n        '0': 'warning',    // 待审批 - 橙色\r\n        '1': 'success',    // 全部通过 - 绿色\r\n        '2': 'danger',     // 已拒绝 - 红色\r\n        '3': 'primary',    // 主管审批中 - 蓝色\r\n        '4': 'primary',    // 总监审批中 - 蓝色\r\n        '5': 'primary',    // 财务主管审批中 - 蓝色\r\n        '6': 'primary'     // 总经理审批中 - 蓝色\r\n      }\r\n      return typeMap[status] || 'info'\r\n    },\r\n\r\n    /** 判断是否可以审批 */\r\n    canApprove(row) {\r\n      // 已完成的状态不能审批\r\n      if (row.approvalStatus === '1' || row.approvalStatus === '2') {\r\n        return false\r\n      }\r\n\r\n      // 这里可以根据当前用户角色和审批状态来判断\r\n      // 简化处理：只要不是已完成状态就可以审批\r\n      // 实际权限控制在后端进行\r\n      return true\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;AAiNA,IAAAA,uBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,0BAAA;MACA;MACAC,iBAAA;MACAC,QAAA;MACA;MACAC,iBAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;QACAC,kBAAA;QACAC,UAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,cAAA;QACAC,UAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,cAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAlB,gBAAA;MACA,KAAAH,WAAA,CAAAG,gBAAA,QAAAiB,MAAA,CAAAC,KAAA,CAAAlB,gBAAA;MACA,KAAAmB,WAAA;IACA;EACA;EACAC,OAAA;IACA,iBACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAK,KAAA;MACA,KAAA/B,OAAA;MACA,IAAAgC,kDAAA,OAAAzB,WAAA,EAAA0B,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA5B,0BAAA,GAAA+B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA7B,KAAA,GAAAgC,QAAA,CAAAhC,KAAA;QACA6B,KAAA,CAAA/B,OAAA;MACA;IACA;IAEA,aACA6B,WAAA,WAAAA,YAAA;MACA,KAAAtB,WAAA,CAAAC,OAAA;MACA,KAAAkB,OAAA;IACA;IACA,aACAU,UAAA,WAAAA,WAAA;MACA,KAAA9B,iBAAA;MACA,KAAA+B,SAAA;MACA,KAAAR,WAAA;IACA;IAEA,iBACAS,6BAAA,WAAAA,8BAAAC,KAAA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAC,MAAA;QACA,KAAAjC,WAAA,CAAAe,iBAAA,GAAAiB,KAAA;QACA,KAAAhC,WAAA,CAAAgB,eAAA,GAAAgB,KAAA;MACA;QACA,KAAAhC,WAAA,CAAAe,iBAAA;QACA,KAAAf,WAAA,CAAAgB,eAAA;MACA;MACA,KAAAM,WAAA;IACA;IAGA,aACAY,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,6DAAAC,cAAA,CAAAC,OAAA,MACA,KAAArC,WAAA,6BAAAsC,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IAEA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,IAAA;QACAC,EAAA;QACAxC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,WAAA;QACAC,kBAAA;QACAC,UAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,cAAA;QAAA;QACAC,UAAA;QACAC,YAAA;QACA8B,YAAA;QACA3B,cAAA;QACA4B,OAAA;MACA;MACA,KAAAf,SAAA;IACA;IAEA,WACAgB,aAAA,WAAAA,cAAAC,GAAA,EAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,UAAA,GAAAF,MAAA;MAEA,IAAAA,MAAA;QACA;QACA,KAAAG,QAAA,gBAAAb,MAAA,CAAAY,UAAA,0DAAAZ,MAAA,CAAAY,UAAA;UACAE,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAA5B,IAAA;UACA,IAAA6B,YAAA;YACAP,MAAA,EAAAA,MAAA;YACAQ,MAAA;UACA;UAEA,IAAAC,qDAAA,EAAAV,GAAA,CAAAJ,EAAA,EAAAY,YAAA,EAAA7B,IAAA,WAAAC,QAAA;YACAsB,MAAA,CAAAS,MAAA,CAAAC,UAAA,IAAArB,MAAA,CAAAY,UAAA;YACAD,MAAA,CAAA9B,OAAA;UACA,GAAAyC,KAAA,WAAAC,KAAA;YACAZ,MAAA,CAAAS,MAAA,CAAAI,QAAA,IAAAxB,MAAA,CAAAY,UAAA,2BAAAW,KAAA,CAAAE,GAAA;UACA;QACA,GAAAH,KAAA;UACAX,MAAA,CAAAS,MAAA,CAAAM,OAAA;QACA;MACA;QACA;QACA,KAAAC,OAAA,sBAAA3B,MAAA,CAAAY,UAAA,sBAAAZ,MAAA,CAAAY,UAAA;UACAE,iBAAA;UACAC,gBAAA;UACAa,YAAA;UACAC,iBAAA;QACA,GAAAzC,IAAA,WAAA0C,IAAA;UAAA,IAAAC,KAAA,GAAAD,IAAA,CAAAC,KAAA;UACA,IAAAd,YAAA;YACAP,MAAA,EAAAA,MAAA;YACAQ,MAAA,EAAAa;UACA;UAEA,IAAAZ,qDAAA,EAAAV,GAAA,CAAAJ,EAAA,EAAAY,YAAA,EAAA7B,IAAA,WAAAC,QAAA;YACAsB,MAAA,CAAAS,MAAA,CAAAC,UAAA,IAAArB,MAAA,CAAAY,UAAA;YACAD,MAAA,CAAA9B,OAAA;UACA,GAAAyC,KAAA,WAAAC,KAAA;YACAZ,MAAA,CAAAS,MAAA,CAAAI,QAAA,IAAAxB,MAAA,CAAAY,UAAA,2BAAAW,KAAA,CAAAE,GAAA;UACA;QACA,GAAAH,KAAA;UACAX,MAAA,CAAAS,MAAA,CAAAM,OAAA;QACA;MACA;IACA;IAEA,WACAM,UAAA,WAAAA,WAAAvB,GAAA;MACA,KAAAjD,QAAA,OAAAsC,cAAA,CAAAC,OAAA,MAAAU,GAAA;MACA,KAAAlD,iBAAA;IACA;IAEA,eACA0E,mBAAA,WAAAA,oBAAAjB,IAAA;MACA,IAAAkB,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAlB,IAAA,KAAAA,IAAA;IACA;IAEA,eACAmB,aAAA,WAAAA,cAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA,iBACAE,gBAAA,WAAAA,iBAAAF,MAAA;MACA,IAAAF,OAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAAE,MAAA;IACA;IAEA,eACAG,UAAA,WAAAA,WAAA9B,GAAA;MACA;MACA,IAAAA,GAAA,CAAAnC,cAAA,YAAAmC,GAAA,CAAAnC,cAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}