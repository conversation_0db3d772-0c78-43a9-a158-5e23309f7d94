package com.ruoyi.daily_expense_approval.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.daily_expense_approval.domain.DailyExpenseApproval;
import com.ruoyi.daily_expense_approval.service.IDailyExpenseApprovalService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 日常花费审批Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/daily_expense_approval/daily_expense_approval")
public class DailyExpenseApprovalController extends BaseController
{
    @Autowired
    private IDailyExpenseApprovalService dailyExpenseApprovalService;

    /**
     * 查询日常花费审批列表
     */
    @PreAuthorize("@ss.hasPermi('daily_expense_approval:daily_expense_approval:list')")
    @GetMapping("/list")
    public TableDataInfo list(DailyExpenseApproval dailyExpenseApproval)
    {
        startPage();
        List<DailyExpenseApproval> list = dailyExpenseApprovalService.selectDailyExpenseApprovalList(dailyExpenseApproval);
        return getDataTable(list);
    }

    /**
     * 查询待审批的日常花费申请列表
     */
    @PreAuthorize("@ss.hasPermi('daily_expense_approval:daily_expense_approval:list')")
    @GetMapping("/pendingList")
    public TableDataInfo pendingList(DailyExpenseApproval dailyExpenseApproval)
    {
        startPage();
        List<DailyExpenseApproval> list = dailyExpenseApprovalService.selectPendingApprovalList(dailyExpenseApproval);
        return getDataTable(list);
    }

    /**
     * 导出日常花费审批列表
     */
    @PreAuthorize("@ss.hasPermi('daily_expense_approval:daily_expense_approval:export')")
    @Log(title = "日常花费审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DailyExpenseApproval dailyExpenseApproval)
    {
        List<DailyExpenseApproval> list = dailyExpenseApprovalService.selectDailyExpenseApprovalList(dailyExpenseApproval);
        ExcelUtil<DailyExpenseApproval> util = new ExcelUtil<DailyExpenseApproval>(DailyExpenseApproval.class);
        util.exportExcel(response, list, "日常花费审批数据");
    }

    /**
     * 获取日常花费审批详细信息
     */
    @PreAuthorize("@ss.hasPermi('daily_expense_approval:daily_expense_approval:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dailyExpenseApprovalService.selectDailyExpenseApprovalById(id));
    }

    /**
     * 新增日常花费审批
     */
    @PreAuthorize("@ss.hasPermi('daily_expense_approval:daily_expense_approval:add')")
    @Log(title = "日常花费审批", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DailyExpenseApproval dailyExpenseApproval)
    {
        return toAjax(dailyExpenseApprovalService.insertDailyExpenseApproval(dailyExpenseApproval));
    }

    /**
     * 修改日常花费审批
     */
    @PreAuthorize("@ss.hasPermi('daily_expense_approval:daily_expense_approval:edit')")
    @Log(title = "日常花费审批", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DailyExpenseApproval dailyExpenseApproval)
    {
        return toAjax(dailyExpenseApprovalService.updateDailyExpenseApproval(dailyExpenseApproval));
    }

    /**
     * 删除日常花费审批
     */
    @PreAuthorize("@ss.hasPermi('daily_expense_approval:daily_expense_approval:remove')")
    @Log(title = "日常花费审批", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dailyExpenseApprovalService.deleteDailyExpenseApprovalByIds(ids));
    }

    /**
     * 审批日常花费申请
     */
    @PreAuthorize("@ss.hasPermi('daily_expense_approval:daily_expense_approval:edit')")
    @Log(title = "日常花费审批", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{id}")
    public AjaxResult approve(@PathVariable("id") Long id, @RequestBody ApprovalRequest approvalRequest)
    {
        try {
            return toAjax(dailyExpenseApprovalService.approveDailyExpense(id, approvalRequest.getAction(), approvalRequest.getRemark()));
        } catch (RuntimeException e) {
            return error(e.getMessage());
        }
    }

    /**
     * 审批请求对象
     */
    public static class ApprovalRequest {
        private String action; // approve 或 reject
        private String remark; // 审批备注

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}
