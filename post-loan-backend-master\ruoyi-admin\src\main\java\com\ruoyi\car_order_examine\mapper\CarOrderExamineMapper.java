package com.ruoyi.car_order_examine.mapper;

import java.util.List;
import com.ruoyi.car_order_examine.domain.CarOrderExamine;

/**
 * 找车费用审批Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface CarOrderExamineMapper 
{
    /**
     * 查询找车费用审批
     *
     * @param id 找车费用审批主键
     * @return 找车费用审批
     */
    public CarOrderExamine selectCarOrderExamineById(String id);

    /**
     * 根据申请编号查询找车费用审批
     *
     * @param applyNo 申请编号
     * @return 找车费用审批
     */
    public CarOrderExamine selectCarOrderExamineByApplyNo(String applyNo);

    /**
     * 根据贷款ID查询找车费用审批
     *
     * @param loanId 贷款ID
     * @return 找车费用审批
     */
    public CarOrderExamine selectCarOrderExamineByLoanId(String loanId);

    /**
     * 查询找车费用审批列表
     * 
     * @param carOrderExamine 找车费用审批
     * @return 找车费用审批集合
     */
    public List<CarOrderExamine> selectCarOrderExamineList(CarOrderExamine carOrderExamine);

    /**
     * 新增找车费用审批
     * 
     * @param carOrderExamine 找车费用审批
     * @return 结果
     */
    public int insertCarOrderExamine(CarOrderExamine carOrderExamine);

    /**
     * 修改找车费用审批
     * 
     * @param carOrderExamine 找车费用审批
     * @return 结果
     */
    public int updateCarOrderExamine(CarOrderExamine carOrderExamine);

    /**
     * 删除找车费用审批
     * 
     * @param id 找车费用审批主键
     * @return 结果
     */
    public int deleteCarOrderExamineById(String id);

    /**
     * 批量删除找车费用审批
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCarOrderExamineByIds(String[] ids);
}
