<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vm_car_order.mapper.VwCarOrderMapper">
    
    <resultMap type="VwCarOrder" id="VwCarOrderResult">
        <result property="id"    column="id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="teamId"    column="team_id"    />
        <result property="garageId"    column="garage_id"    />
        <result property="libraryStatus"    column="library_status"    />
        <result property="inboundTime"    column="inbound_time"    />
        <result property="outboundTime"    column="outbound_time"    />
        <result property="locatingCommission"    column="locating_commission"    />
        <result property="keyStatus"    column="key_status"    />
        <result property="keyTime"    column="key_time"    />
        <result property="collectionMethod"    column="collection_method"    />
        <result property="status"    column="status"    />
        <result property="allocationTime"    column="allocation_time"    />
        <result property="keyProvince"    column="key_province"    />
        <result property="keyCity"    column="key_city"    />
        <result property="keyBorough"    column="key_borough"    />
        <result property="keyAddress"    column="key_address"    />
        <result property="customerName"    column="customer_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="loanId"    column="loan_id"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="nickName"    column="nick_name"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="jgName"    column="jg_name"    />
        <result property="jgStatus"    column="jg_status"    />
        <result property="carDetailAddress"    column="car_detail_address"    />
        <result property="carStatus"    column="car_status"    />
        <result property="gpsStatus"    column="gps_status"    />
        <result property="teamName"    column="team_name"    />
        <result property="garageName"    column="garage_name"    />
        <result property="slippageStatus"    column="slippage_status"    />
        <result property="overdueAmt"    column="overdue_amt"    />
    </resultMap>

    <sql id="selectVwCarOrderVo">
        select * from vw_car_order
    </sql>

    <select id="selectVwCarOrderList" parameterType="VwCarOrder" resultMap="VwCarOrderResult">
        <include refid="selectVwCarOrderVo"/>
        <where>  
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="garageId != null "> and garage_id = #{garageId}</if>
            <if test="libraryStatus != null "> and library_status = #{libraryStatus}</if>
            <if test="inboundTime != null "> and inbound_time = #{inboundTime}</if>
            <if test="outboundTime != null "> and outbound_time = #{outboundTime}</if>
            <if test="locatingCommission != null "> and locating_commission = #{locatingCommission}</if>
            <if test="keyStatus != null "> and key_status = #{keyStatus}</if>
            <if test="keyTime != null "> and key_time = #{keyTime}</if>
            <if test="collectionMethod != null "> and collection_method = #{collectionMethod}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="startTime != null and endTime != null"> AND allocation_time BETWEEN #{startTime} AND #{endTime}</if>
            <if test="keyProvince != null  and keyProvince != ''"> and key_province = #{keyProvince}</if>
            <if test="keyCity != null  and keyCity != ''"> and key_city = #{keyCity}</if>
            <if test="keyBorough != null  and keyBorough != ''"> and key_borough = #{keyBorough}</if>
            <if test="keyAddress != null  and keyAddress != ''"> and key_address = #{keyAddress}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="plateNo != null  and plateNo != ''"> and plate_no = #{plateNo}</if>
            <if test="jgName != null  and jgName != ''"> and jg_name like concat('%', #{jgName}, '%')</if>
            <if test="jgStatus != null  and jgStatus != ''"> and jg_status = #{jgStatus}</if>
            <if test="carDetailAddress != null  and carDetailAddress != ''"> and car_detail_address = #{carDetailAddress}</if>
            <if test="carStatus != null  and carStatus != ''"> and car_status = #{carStatus}</if>
            <if test="gpsStatus != null  and gpsStatus != ''"> and gps_status = #{gpsStatus}</if>
            <if test="teamName != null  and teamName != ''"> and team_name like concat('%', #{teamName}, '%')</if>
            <if test="garageName != null  and garageName != ''"> and garage_name like concat('%', #{garageName}, '%')</if>
        </where>
        GROUP BY id
    </select>
    
    <select id="selectVwCarOrderById" parameterType="String" resultMap="VwCarOrderResult">
        <include refid="selectVwCarOrderVo"/>
        where id = #{id}
        LIMIT 1
    </select>

    <insert id="insertVwCarOrder" parameterType="VwCarOrder">
        insert into car_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="teamId != null">team_id,</if>
            <if test="garageId != null">garage_id,</if>
            <if test="locatingCommission != null">locating_commission,</if>
            <if test="keyStatus != null">key_status,</if>
            <if test="keyTime != null">key_time,</if>
            <if test="collectionMethod != null">collection_method,</if>
            <if test="status != null">status,</if>
            <if test="allocationTime != null">allocation_time,</if>
            <if test="keyProvince != null">key_province,</if>
            <if test="keyCity != null">key_city,</if>
            <if test="keyBorough != null">key_borough,</if>
            <if test="keyAddress != null">key_address,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="garageId != null">#{garageId},</if>
            <if test="locatingCommission != null">#{locatingCommission},</if>
            <if test="keyStatus != null">#{keyStatus},</if>
            <if test="keyTime != null">#{keyTime},</if>
            <if test="collectionMethod != null">#{collectionMethod},</if>
            <if test="status != null">#{status},</if>
            <if test="allocationTime != null">#{allocationTime},</if>
            <if test="keyProvince != null">#{keyProvince},</if>
            <if test="keyCity != null">#{keyCity},</if>
            <if test="keyBorough != null">#{keyBorough},</if>
            <if test="keyAddress != null">#{keyAddress},</if>
         </trim>
    </insert>

    <update id="updateVwCarOrder" parameterType="VwCarOrder">
        update car_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="applyNo != null">apply_no = #{applyNo},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="garageId != null">garage_id = #{garageId},</if>
            <if test="locatingCommission != null">locating_commission = #{locatingCommission},</if>
            <if test="keyStatus != null">key_status = #{keyStatus},</if>
            <if test="keyTime != null">key_time = #{keyTime},</if>
            <if test="collectionMethod != null">collection_method = #{collectionMethod},</if>
            <if test="status != null">status = #{status},</if>
            <if test="allocationTime != null">allocation_time = #{allocationTime},</if>
            <if test="keyProvince != null">key_province = #{keyProvince},</if>
            <if test="keyCity != null">key_city = #{keyCity},</if>
            <if test="keyBorough != null">key_borough = #{keyBorough},</if>
            <if test="keyAddress != null">key_address = #{keyAddress},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVwCarOrderById" parameterType="String">
        delete from car_order where id = #{id}
    </delete>

    <delete id="deleteVwCarOrderByIds" parameterType="String">
        delete from car_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>