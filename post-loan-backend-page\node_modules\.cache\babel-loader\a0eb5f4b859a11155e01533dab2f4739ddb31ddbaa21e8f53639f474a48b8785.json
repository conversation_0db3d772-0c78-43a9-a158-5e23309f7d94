{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _slicedToArray2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/slicedToArray.js\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/objectSpread2.js\"));\nrequire(\"core-js/modules/es.array.join.js\");\nrequire(\"core-js/modules/es.array.map.js\");\nrequire(\"core-js/modules/es.array.push.js\");\nrequire(\"core-js/modules/es.number.constructor.js\");\nrequire(\"core-js/modules/es.number.to-fixed.js\");\nrequire(\"core-js/modules/es.object.to-string.js\");\nrequire(\"core-js/modules/esnext.iterator.constructor.js\");\nrequire(\"core-js/modules/esnext.iterator.map.js\");\nvar _vw_account_loan = require(\"@/api/vw_account_loan/vw_account_loan\");\nvar _auth = require(\"@/utils/auth\");\nvar _userInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/userInfo.vue\"));\nvar _carInfo = _interopRequireDefault(require(\"@/layout/components/Dialog/carInfo.vue\"));\nvar _loanReminderLog = _interopRequireDefault(require(\"@/layout/components/Dialog/loanReminderLog.vue\"));\nvar _loanReminderLogSubmit = _interopRequireDefault(require(\"@/layout/components/Dialog/loanReminderLogSubmit.vue\"));\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  components: {\n    userInfo: _userInfo.default,\n    carInfo: _carInfo.default,\n    LoanReminderLog: _loanReminderLog.default,\n    LoanReminderLogSubmit: _loanReminderLogSubmit.default\n  },\n  props: {\n    value: [String, Object, Array],\n    // 上传接口地址\n    action: {\n      type: String,\n      // default: \"/common/upload\"\n      default: '/common/ossupload'\n    },\n    // 上传携带的参数\n    data: {\n      type: Object\n    }\n  },\n  name: 'Vw_account_loan',\n  data: function data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 控制更多筛选条件显示\n      showMore: false,\n      // 总条数\n      total: 0,\n      // VIEW表格数据\n      vw_account_loanList: [],\n      // 是否显示弹出层\n      logopen: false,\n      //催记日志\n      currentRow: {},\n      //dialog传入数据\n      urgeBackopen: false,\n      //催回结清\n      derateopen: false,\n      //减免结清\n      commuteopen: false,\n      //发起代偿\n      detailShow: false,\n      //查看日志详情\n      lenderShow: false,\n      //贷款人信息\n      customerInfo: {\n        customerId: '',\n        applyId: ''\n      },\n      carShow: false,\n      // 车辆信息\n      plateNo: '',\n      // 车辆编号\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 15,\n        customerName: null,\n        certId: null,\n        plateNo: null,\n        partnerId: null,\n        jgName: null,\n        slippageStatus: null,\n        followStatus: null,\n        followUp: null,\n        allocationTime: null,\n        startTime: '',\n        endTime: '',\n        isExtension: '',\n        carStatus: '',\n        isFindCar: ''\n      },\n      bankList: [{\n        value: 'EO00000010',\n        label: '苏银金租'\n      }, {\n        value: 'IO00000006',\n        label: '浙商银行'\n      }, {\n        value: 'IO00000007',\n        label: '中关村银行'\n      }, {\n        value: 'IO00000008',\n        label: '蓝海银行'\n      }, {\n        value: 'IO00000009',\n        label: '华瑞银行'\n      }, {\n        value: 'IO00000010',\n        label: '皖新租赁'\n      }],\n      isExtensionList: [{\n        label: '否',\n        value: '0'\n      }, {\n        label: '是',\n        value: '1'\n      }],\n      slippageList: [{\n        label: '提醒',\n        value: 1\n      }, {\n        label: '电催',\n        value: 2\n      }, {\n        label: '上访',\n        value: 3\n      }, {\n        label: '逾期30-60',\n        value: 4\n      }, {\n        label: '逾期60+',\n        value: 5\n      }],\n      followUpList: [{\n        label: '继续联系',\n        value: 1\n      }, {\n        label: '约定还款',\n        value: 2\n      }, {\n        label: '无法跟进',\n        value: 3\n      }],\n      accountList: [],\n      //银行账户列表\n      // 表单校验\n      rules: {},\n      dialogImageUrl: '',\n      dialogVisible: false,\n      textarea: null,\n      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action,\n      // 上传的图片服务器地址\n      headers: {\n        Authorization: 'Bearer ' + (0, _auth.getToken)()\n      },\n      reminderList: [],\n      //催记列表\n      reminderShow: false,\n      urgeform: {},\n      derateform: {},\n      radio: 0,\n      logList: [],\n      //催记日志\n      logDetail: {},\n      //催记日志详情\n      logForm: {\n        loanId: null,\n        pageNum: 1,\n        pageSize: 15\n      },\n      logtotal: 0,\n      urgeAllMoney: 0,\n      //催回结清总欠款\n      derateAllMoney: 0,\n      //减免结清总欠款\n      loanId: null,\n      addId: null,\n      //催回、减免新增id\n      jmForm: {},\n      chForm: {},\n      urgeformbtotalMoney: 0,\n      urgeformdtotalMoney: 0,\n      urgeformliquidatedDamages: 0,\n      urgeformoneCommutation: 0,\n      derateformbtotalMoney: 0,\n      derateformdtotalMoney: 0,\n      derateformliquidatedDamages: 0,\n      derateformoneCommutation: 0,\n      trialForm: {},\n      trialFormprincipal: 0,\n      trialFormboverdueAmount: 0,\n      trialForminterest: 0,\n      trialFormall: 0,\n      trialFormdoverdueAmount: 0,\n      trialFormliquidatedDamages: 0,\n      trialFormtotal: 0,\n      trialFormotherDebt: 0,\n      dkyqMoney: 0,\n      bankyqMoney: 0,\n      lendingBank: null,\n      OrderingChannel: null,\n      logLoanId: null,\n      //嵌入催记日志组件的loanId\n      submitLoanId: null,\n      //嵌入提交催记组件的loanId\n      carStatusList: [{\n        label: '省内正常行驶',\n        value: '1'\n      }, {\n        label: '省外正常行驶',\n        value: '2'\n      }, {\n        label: '抵押',\n        value: '3'\n      }, {\n        label: '疑似抵押',\n        value: '4'\n      }, {\n        label: '疑似黑车',\n        value: '5'\n      }, {\n        label: '已入库',\n        value: '6'\n      }, {\n        label: '车在法院',\n        value: '7'\n      }, {\n        label: '已法拍',\n        value: '8'\n      }, {\n        label: '协商卖车',\n        value: '9'\n      }],\n      isFindCarList: [{\n        label: '未派单',\n        value: '0'\n      }, {\n        label: '已派单',\n        value: '1'\n      }]\n    };\n  },\n  created: function created() {\n    this.getList();\n    this.getAccountList();\n  },\n  methods: {\n    getAccountList: function getAccountList() {\n      var _this = this;\n      (0, _vw_account_loan.get_bank_account)().then(function (response) {\n        _this.accountList = response.rows;\n      });\n    },\n    handleEstimateBadDebt: function handleEstimateBadDebt(row) {\n      var _this2 = this;\n      var data = {\n        id: row.loanId,\n        badDebt: 1\n      };\n      (0, _vw_account_loan.update_loan_list)(data).then(function (response) {\n        if (response.code == 200) {\n          _this2.$modal.msgSuccess('预估呆账成功');\n        } else {\n          _this2.$modal.msgError('预估呆账失败');\n        }\n      });\n    },\n    trialSub: function trialSub() {\n      var _this3 = this;\n      var data = {\n        applyId: this.trialForm.applyId,\n        id: this.trialForm.id,\n        loanId: this.trialForm.loanId,\n        loanAmount: this.trialForm.loanAmount,\n        partnerId: this.trialForm.partnerId\n      };\n      (0, _vw_account_loan.dc_submit_order)(data).then(function (response) {\n        _this3.trialFormprincipal = response.data.principal || 0;\n        _this3.trialForm.defaultInterest = response.data.defaultInterest || 0;\n        _this3.trialForminterest = response.data.interest || 0;\n        _this3.trialFormall = response.data.btotalMoney || 0;\n        _this3.trialFormdoverdueAmount = response.data.dtotalMoney || 0;\n        _this3.trialFormliquidatedDamages = response.data.liquidatedDamages || 0;\n        _this3.trialFormtotal = Number(_this3.trialFormall + _this3.trialFormdoverdueAmount + _this3.trialFormliquidatedDamages + _this3.trialFormotherDebt).toFixed(2);\n      });\n    },\n    submitDerate: function submitDerate() {\n      var _this4 = this;\n      if (this.derateformbtotalMoney && !this.derateform.accountNumber1) {\n        this.$modal.msgError('请选择银行结清账号');\n        return;\n      }\n      if (this.derateformdtotalMoney && !this.derateform.accountNumber2) {\n        this.$modal.msgError('请选择代扣剩余账号');\n        return;\n      }\n      if (this.derateformliquidatedDamages && !this.derateform.accountNumber3) {\n        this.$modal.msgError('请选择违约金账号');\n        return;\n      }\n      if (this.derateform.otherDebt && !this.derateform.accountNumber4) {\n        this.$modal.msgError('请选择其他欠款账号');\n        return;\n      }\n      if (this.derateformoneCommutation && !this.derateform.accountNumber5) {\n        this.$modal.msgError('请选择单期代偿金账号');\n        return;\n      }\n      var data = {\n        customerName: this.derateform.customerName,\n        orgName: this.derateform.orgName,\n        bank: this.derateform.bank,\n        loanAmount: this.derateform.loanAmount,\n        btotalMoney: this.derateform.btotalMoney,\n        accountNumber1: this.derateform.accountNumber1,\n        dtotalMoney: this.derateform.dtotalMoney,\n        accountNumber2: this.derateform.accountNumber2,\n        liquidatedDamages: this.derateform.liquidatedDamages,\n        accountNumber3: this.derateform.accountNumber3,\n        otherDebt: this.derateform.otherDebt,\n        accountNumber4: this.derateform.accountNumber4,\n        oneCommutation: this.derateform.oneCommutation,\n        accountNumber5: this.derateform.accountNumber5,\n        totalMoney: this.derateAllMoney,\n        id: this.addId,\n        status: 2,\n        examineStatus: 1\n      };\n      (0, _vw_account_loan.trial_balance_put)(data).then(function (response) {\n        if (response.code == 200) {\n          _this4.$modal.msgSuccess('提交成功');\n          _this4.derateopen = false;\n          _this4.derateform = {};\n          _this4.derateformbtotalMoney = 0;\n          _this4.derateformdtotalMoney = 0;\n          _this4.derateformliquidatedDamages = 0;\n          _this4.derateformoneCommutation = 0;\n        }\n      });\n    },\n    submitUrge: function submitUrge() {\n      var _this5 = this;\n      if (this.urgeformbtotalMoney && !this.urgeform.accountNumber1) {\n        this.$modal.msgError('请选择银行结清账号');\n        return;\n      }\n      if (this.urgeformdtotalMoney && !this.urgeform.accountNumber2) {\n        this.$modal.msgError('请选择代扣剩余账号');\n        return;\n      }\n      if (this.urgeformliquidatedDamages && !this.urgeform.accountNumber3) {\n        this.$modal.msgError('请选择违约金账号');\n        return;\n      }\n      if (this.urgeform.otherDebt && !this.urgeform.accountNumber4) {\n        this.$modal.msgError('请选择其他欠款账号');\n        return;\n      }\n      if (this.urgeformoneCommutation && !this.urgeform.accountNumber5) {\n        this.$modal.msgError('请选择单期代偿金账号');\n        return;\n      }\n      var data = {\n        customerName: this.urgeform.customerName,\n        orgName: this.urgeform.orgName,\n        bank: this.urgeform.bank,\n        loanAmount: this.urgeform.loanAmount,\n        btotalMoney: this.urgeform.btotalMoney,\n        accountNumber1: this.urgeform.accountNumber1,\n        dtotalMoney: this.urgeform.dtotalMoney,\n        accountNumber2: this.urgeform.accountNumber2,\n        liquidatedDamages: this.urgeform.liquidatedDamages,\n        accountNumber3: this.urgeform.accountNumber3,\n        otherDebt: this.urgeform.otherDebt,\n        accountNumber4: this.urgeform.accountNumber4,\n        oneCommutation: this.urgeform.oneCommutation,\n        accountNumber5: this.urgeform.accountNumber5,\n        totalMoney: this.urgeAllMoney,\n        id: this.addId,\n        status: 1,\n        examineStatus: 1\n      };\n      (0, _vw_account_loan.trial_balance_put)(data).then(function (response) {\n        if (response.code == 200) {\n          _this5.$modal.msgSuccess('提交成功');\n          _this5.urgeBackopen = false;\n          _this5.urgeform = {};\n          _this5.urgeformbtotalMoney = 0;\n          _this5.urgeformdtotalMoney = 0;\n          _this5.urgeformliquidatedDamages = 0;\n          _this5.urgeformoneCommutation = 0;\n        }\n      });\n    },\n    onInputLimit: function onInputLimit(key, value) {\n      var min = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;\n      var max = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 100;\n      var val = Number(value);\n      if (isNaN(val)) val = min;\n      if (val > max) val = max;\n      if (val < min) val = min;\n      this.trialForm[key] = Number(val);\n    },\n    cancelDetail: function cancelDetail() {\n      this.detailShow = false;\n    },\n    //查看车牌信息\n    checkCar: function checkCar(plateNo) {\n      this.plateNo = plateNo;\n      this.carShow = true;\n    },\n    //查看贷款人信息\n    checkLender: function checkLender(customerInfo) {\n      this.customerInfo = customerInfo;\n      this.lenderShow = true;\n    },\n    planBth: function planBth(row) {\n      var data = {\n        partnerId: row.partnerId,\n        applyId: row.applyId\n      };\n      if (row.partnerId == 'IO00000008') {\n        this.$router.push({\n          path: '/repayment/repayment_plan/lhindex',\n          query: data\n        });\n      } else if (row.partnerId == 'EO00000010') {\n        this.$router.push({\n          path: '/repayment/repayment_plan/syindex',\n          query: data\n        });\n      } else if (row.partnerId == 'IO00000006') {\n        this.$router.push({\n          path: '/repayment/repayment_plan/zsindex',\n          query: data\n        });\n      } else if (row.partnerId == 'IO00000007') {\n        this.$router.push({\n          path: '/repayment/repayment_plan/zgcindex',\n          query: data\n        });\n      } else if (row.partnerId == 'IO00000009') {\n        this.$router.push({\n          path: '/repayment/repayment_plan/hrindex',\n          query: data\n        });\n      } else {\n        this.$router.push({\n          path: '/repayment/repayment_plan/wxindex',\n          query: data\n        });\n      }\n    },\n    checkReminder: function checkReminder(id) {\n      var _this6 = this;\n      var data = {\n        loanId: id,\n        urgeStatus: 2\n      };\n      (0, _vw_account_loan.loan_reminder_order)(data).then(function (response) {\n        _this6.reminderList = response.rows;\n        _this6.reminderShow = true;\n      });\n    },\n    cancelcommute: function cancelcommute() {\n      this.commuteopen = false;\n    },\n    initiate: function initiate(row) {\n      var _this7 = this;\n      //发起代偿\n      var data = {\n        loanId: row.loanId\n      };\n      (0, _vw_account_loan.loan_compensation_order)(data).then(function (response) {\n        if (response.data) {\n          _this7.trialForm = response.data;\n          _this7.trialForm.loanAmount = row.contractAmt || 0;\n          _this7.trialForm.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0;\n          _this7.trialFormprincipal = response.data.trialBalance ? response.data.trialBalance.principal : 0;\n          _this7.trialFormboverdueAmount = row.boverdueAmount || 0;\n          _this7.trialForminterest = response.data.trialBalance ? response.data.trialBalance.interest : 0;\n          _this7.trialFormall = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0;\n          _this7.trialFormdoverdueAmount = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0;\n          _this7.trialFormliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0;\n          _this7.trialFormotherDebt = response.data.otherDebt ? response.data.otherDebt : 0;\n          console.log(_this7.trialFormall, _this7.trialFormdoverdueAmount, _this7.trialFormliquidatedDamages, _this7.trialFormotherDebt);\n          _this7.trialFormtotal = Number(_this7.trialFormall + _this7.trialFormdoverdueAmount + _this7.trialFormliquidatedDamages + _this7.trialFormotherDebt).toFixed(2);\n          _this7.trialForm.image = response.data.image ? response.data.image.split(',') : [];\n          _this7.commuteopen = true;\n        } else {\n          _this7.addTrial(row);\n          _this7.trialForm.image = [];\n        }\n      });\n    },\n    addTrial: function addTrial(row) {\n      var _this8 = this;\n      console.log(row);\n      var data = {\n        id: row.id,\n        applyId: row.applyId,\n        loanId: row.loanId,\n        customerId: row.customerId,\n        customerName: row.customerName,\n        salesman: row.nickName,\n        orgName: row.jgName,\n        partnerId: row.partnerId,\n        bank: row.orgName,\n        loanAmount: row.contractAmt,\n        examineStatus: 0\n      };\n      (0, _vw_account_loan.add_Trial_order)(data).then(function (response) {\n        _this8.trialForm = response.data || {};\n        _this8.commuteopen = true;\n      });\n    },\n    cancelderate: function cancelderate() {\n      this.derateform = {};\n      this.jmForm = {};\n      this.derateformbtotalMoney = 0;\n      this.derateformdtotalMoney = 0;\n      this.derateformliquidatedDamages = 0;\n      this.derateformoneCommutation = 0;\n      this.derateAllMoney = 0;\n      this.derateopen = false;\n    },\n    handleInput3: function handleInput3(value) {\n      this.trialFormotherDebt = Number(value);\n      this.trialFormtotal = Number(this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt).toFixed(2);\n    },\n    handleInput2: function handleInput2(value) {\n      this.derateform.otherDebt = Number(value);\n      this.derateAllMoney = Number(this.derateform.btotalMoney + this.derateform.dtotalMoney + this.derateform.liquidatedDamages + this.derateform.otherDebt + this.derateform.oneCommutation).toFixed(2);\n    },\n    handleInput1: function handleInput1(value) {\n      this.urgeform.otherDebt = Number(value);\n      this.urgeAllMoney = Number(this.urgeform.btotalMoney + this.urgeform.dtotalMoney + this.urgeform.liquidatedDamages + this.urgeform.otherDebt + this.urgeform.oneCommutation).toFixed(2);\n    },\n    derateSettle: function derateSettle(row) {\n      var _this9 = this;\n      var data = {\n        loanId: row.loanId,\n        status: 2,\n        pageSize: 1,\n        pageNum: 1\n      };\n      this.loanId = row.loanId;\n      (0, _vw_account_loan.trial_balance_order)(data).then(function (response) {\n        if (response.data) {\n          _this9.derateform = response.data;\n          if (!response.data.customerName) {\n            _this9.derateform.customerName = row.customerName;\n          }\n          if (!response.data.orgName) {\n            _this9.derateform.orgName = row.jgName;\n          }\n          if (!response.data.bank) {\n            _this9.derateform.bank = row.orgName;\n          }\n          if (!response.data.loanAmount) {\n            _this9.derateform.loanAmount = row.contractAmt;\n          }\n          _this9.derateform.btotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0;\n          _this9.derateform.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0;\n          _this9.derateform.oneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0;\n          _this9.derateform.liquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0;\n          _this9.derateformbtotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0;\n          _this9.derateformdtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0;\n          _this9.derateformoneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0;\n          _this9.derateformliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0;\n          _this9.derateAllMoney = Number(_this9.derateform.btotalMoney + _this9.derateform.dtotalMoney + _this9.derateform.liquidatedDamages + _this9.derateform.oneCommutation).toFixed(2);\n          _this9.addId = response.data.id;\n          _this9.jmForm = response.data;\n        } else {\n          _this9.postTrial(row, 2);\n          // this.derateform = {}\n          // this.derateform.customerName = row.customerName\n          // this.derateform.orgName = row.jgName\n          // this.derateform.bank = row.orgName\n          // this.derateform.loanAmount = row.contractAmt\n        }\n      }).catch(function (error) {});\n      this.derateopen = true;\n    },\n    cancelurgeBack: function cancelurgeBack() {\n      this.urgeform = {};\n      this.chForm = {};\n      this.urgeformbtotalMoney = 0;\n      this.urgeformdtotalMoney = 0;\n      this.urgeformliquidatedDamages = 0;\n      this.urgeformoneCommutation = 0;\n      this.urgeAllMoney = 0;\n      this.urgeBackopen = false;\n    },\n    getUrgeMoney: function getUrgeMoney(e) {\n      var _this0 = this;\n      var data = {};\n      if (e == 1) {\n        data = this.chForm;\n      } else {\n        data = this.jmForm;\n      }\n      (0, _vw_account_loan.trial_submit_order)(data).then(function (response) {\n        if (e == 1) {\n          // this.urgeform = response.data\n          _this0.urgeform.btotalMoney = response.data.btotalMoney || 0;\n          _this0.urgeform.dtotalMoney = response.data.dtotalMoney || 0;\n          _this0.urgeform.liquidatedDamages = response.data.liquidatedDamages || 0;\n          _this0.urgeform.oneCommutation = response.data.oneCommutation || 0;\n          _this0.urgeformbtotalMoney = response.data.btotalMoney || 0;\n          _this0.urgeformdtotalMoney = response.data.dtotalMoney || 0;\n          _this0.urgeformliquidatedDamages = response.data.liquidatedDamages || 0;\n          _this0.urgeformoneCommutation = response.data.oneCommutation || 0;\n          _this0.urgeAllMoney = Number(_this0.urgeform.btotalMoney + _this0.urgeform.dtotalMoney + _this0.urgeform.liquidatedDamages + _this0.urgeform.otherDebt + _this0.urgeform.oneCommutation).toFixed(2);\n        } else {\n          // this.derateform = response.data\n          _this0.derateform.btotalMoney = response.data.btotalMoney || 0;\n          _this0.derateform.dtotalMoney = response.data.dtotalMoney || 0;\n          _this0.derateform.liquidatedDamages = response.data.liquidatedDamages || 0;\n          _this0.derateform.oneCommutation = response.data.oneCommutation || 0;\n          _this0.derateformbtotalMoney = response.data.btotalMoney || 0;\n          _this0.derateformdtotalMoney = response.data.dtotalMoney || 0;\n          _this0.derateformliquidatedDamages = response.data.liquidatedDamages || 0;\n          _this0.derateformoneCommutation = response.data.oneCommutation || 0;\n          _this0.derateAllMoney = Number(_this0.derateform.btotalMoney + _this0.derateform.dtotalMoney + _this0.derateform.liquidatedDamages + _this0.derateform.otherDebt + _this0.derateform.oneCommutation).toFixed(2);\n        }\n      });\n    },\n    urgeBackSettle: function urgeBackSettle(row) {\n      var _this1 = this;\n      var data = {\n        loanId: row.loanId,\n        status: 1,\n        pageSize: 1,\n        pageNum: 1\n      };\n      this.loanId = row.loanId;\n      (0, _vw_account_loan.trial_balance_order)(data).then(function (response) {\n        if (response.data) {\n          _this1.urgeform = response.data;\n          if (!response.data.customerName) {\n            _this1.urgeform.customerName = row.customerName;\n          }\n          if (!response.data.orgName) {\n            _this1.urgeform.orgName = row.jgName;\n          }\n          if (!response.data.bank) {\n            _this1.urgeform.bank = row.orgName;\n          }\n          if (!response.data.loanAmount) {\n            _this1.urgeform.loanAmount = row.contractAmt;\n          }\n          _this1.urgeform.btotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0;\n          _this1.urgeform.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0;\n          _this1.urgeform.liquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0;\n          _this1.urgeform.oneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0;\n          _this1.urgeformbtotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0;\n          _this1.urgeformdtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0;\n          _this1.urgeformliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0;\n          _this1.urgeformoneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0;\n          _this1.urgeAllMoney = Number(_this1.urgeform.btotalMoney + _this1.urgeform.dtotalMoney + _this1.urgeform.liquidatedDamages + _this1.urgeform.oneCommutation).toFixed(2);\n          _this1.addId = response.data.id;\n          _this1.chForm = response.data;\n        } else {\n          _this1.postTrial(row, 1);\n          // this.urgeform = {}\n          // this.urgeform.customerName = row.customerName\n          // this.urgeform.orgName = row.jgName\n          // this.urgeform.bank = row.orgName\n          // this.urgeform.loanAmount = row.contractAmt\n        }\n        _this1.urgeBackopen = true;\n      }).catch(function (error) {});\n    },\n    postTrial: function postTrial(row, e) {\n      var _this10 = this;\n      var data = {\n        applyId: row.applyId,\n        loanId: row.loanId,\n        customerName: row.customerName,\n        orgName: row.jgName,\n        loanAmount: row.contractAmt,\n        bank: row.orgName,\n        partnerId: row.partnerId,\n        status: e,\n        salesman: row.nickName,\n        overdueDays: row.boverdueDays,\n        examineStatus: 0\n      };\n      (0, _vw_account_loan.trial_balance_post)(data).then(function (response) {\n        _this10.addId = response.id;\n        if (e == 1) {\n          _this10.urgeform = response.data;\n          _this10.chForm = response.data;\n        } else {\n          _this10.derateform = response.data;\n          _this10.jmForm = response.data;\n        }\n      });\n    },\n    cancelLog: function cancelLog() {\n      this.logopen = false;\n    },\n    logView: function logView(row) {\n      this.logLoanId = row.loanId;\n      this.$refs.loanReminderLog.openLogDialog();\n    },\n    handleSubmitReminder: function handleSubmitReminder(row) {\n      var _this11 = this;\n      this.submitLoanId = row.loanId;\n      this.$nextTick(function () {\n        _this11.$refs.loanReminderLogSubmit.openDialog();\n      });\n    },\n    logView2: function logView2() {\n      var _this12 = this;\n      (0, _vw_account_loan.loan_reminder_order)(this.logForm).then(function (response) {\n        _this12.logList = response.rows;\n        _this12.logtotal = response.total;\n        _this12.logopen = true;\n      }).catch(function (error) {});\n    },\n    handleChange: function handleChange(value) {\n      this.queryParams.orgId = value;\n    },\n    /** 查询VIEW列表 */getList: function getList() {\n      var _this13 = this;\n      console.log(this.$store.state.user);\n      this.loading = true;\n      (0, _vw_account_loan.listVw_account_loan)(this.queryParams).then(function (response) {\n        _this13.vw_account_loanList = response.rows;\n        _this13.total = response.total;\n        _this13.loading = false;\n      });\n    },\n    // 表单重置\n    reset: function reset() {\n      this.form = {\n        customerName: null,\n        jgName: null,\n        orgName: null,\n        boverdueAmount: null,\n        doverdueAmount: null\n      };\n      this.resetForm('form');\n    },\n    /** 搜索按钮操作 */handleQuery: function handleQuery() {\n      if (this.queryParams.allocationTime) {\n        this.queryParams.startTime = this.queryParams.allocationTime[0];\n        this.queryParams.endTime = this.queryParams.allocationTime[1];\n      }\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */resetQuery: function resetQuery() {\n      this.queryParams.customerName = null;\n      this.queryParams.certId = null;\n      this.queryParams.plateNo = null;\n      this.queryParams.partnerId = null;\n      this.queryParams.jgName = null;\n      this.queryParams.slippageStatus = null;\n      this.queryParams.followStatus = null;\n      this.queryParams.followUp = null;\n      this.queryParams.allocationTime = null;\n      this.queryParams.startTime = null;\n      this.queryParams.endTime = null;\n      this.queryParams.isExtension = null;\n      this.queryParams.carStatus = '';\n      this.queryParams.isFindCar = '';\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.ids = selection.map(function (item) {\n        return item.id;\n      });\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */handleAdd: function handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = '添加VIEW';\n    },\n    calcMoney: function calcMoney(proportionKey, moneyKey, val) {\n      // 只做金额计算和保留两位小数\n      this.trialForm[moneyKey] = (val * this.trialFormall / 100).toFixed(2);\n    },\n    /** 提交按钮 */submitForm: function submitForm() {\n      var _this14 = this;\n      var keys = ['fxjProportion', 'qdProportion', 'gmjProportion', 'kjjProportion', 'kjczProportion', 'sbczProportion'];\n      // 计算比例之和\n      var total = keys.reduce(function (sum, key) {\n        return sum + Number(_this14.trialForm[key] || 0);\n      }, 0);\n      if (total !== 100) {\n        this.$message.error('六项比例之和必须等于100！');\n        return;\n      }\n\n      // 金额和账号类型字段映射\n      var moneyAccountMap = [{\n        money: 'fxjMoney',\n        account: 'fxjAccount',\n        label: '风险金账号类型'\n      }, {\n        money: 'qdMoney',\n        account: 'qdAccount',\n        label: '渠道账号类型'\n      }, {\n        money: 'gmjMoney',\n        account: 'gmjAccount',\n        label: '广明借账号类型'\n      }, {\n        money: 'kjjMoney',\n        account: 'kjjAccount',\n        label: '科技借账号类型'\n      }, {\n        money: 'kjczMoney',\n        account: 'kjczAccount',\n        label: '科技出资账号类型'\n      }, {\n        money: 'sbczMoney',\n        account: 'sbczAccount',\n        label: '守邦出资账号类型'\n      }];\n\n      // 校验每个金额>0时账号类型必填\n      for (var _i = 0, _moneyAccountMap = moneyAccountMap; _i < _moneyAccountMap.length; _i++) {\n        var item = _moneyAccountMap[_i];\n        if (Number(this.trialForm[item.money]) > 0 && !this.trialForm[item.account]) {\n          this.$message.warning(\"\\u8BF7\\u5148\\u9009\\u62E9\".concat(item.label));\n          return;\n        }\n      }\n      var data = {\n        id: this.trialForm.id,\n        fxjProportion: this.trialForm.fxjProportion,\n        fxjMoney: this.trialForm.fxjMoney,\n        fxjAccount: this.trialForm.fxjAccount,\n        qdProportion: this.trialForm.qdProportion,\n        qdMoney: this.trialForm.qdMoney,\n        qdAccount: this.trialForm.qdAccount,\n        gmjProportion: this.trialForm.gmjProportion,\n        gmjMoney: this.trialForm.gmjMoney,\n        gmjAccount: this.trialForm.gmjAccount,\n        kjjProportion: this.trialForm.kjjProportion,\n        kjjMoney: this.trialForm.kjjMoney,\n        kjjAccount: this.trialForm.kjjAccount,\n        kjczProportion: this.trialForm.kjczProportion,\n        kjczMoney: this.trialForm.kjczMoney,\n        kjczAccount: this.trialForm.kjczAccount,\n        sbczProportion: this.trialForm.sbczProportion,\n        sbczMoney: this.trialForm.sbczMoney,\n        sbczAccount: this.trialForm.sbczAccount,\n        otherDebt: this.trialFormotherDebt,\n        totalMoney: this.trialFormtotal,\n        image: this.trialForm.image.map(function (item) {\n          return item.response;\n        }).join(','),\n        examineStatus: 1,\n        repaymentType: 0\n      };\n      console.log(data);\n      (0, _vw_account_loan.sub_Trial_order)(data).then(function (response) {\n        if (response.code == 200) {\n          _this14.$modal.msgSuccess('提交成功');\n          _this14.commuteopen = false;\n        }\n      });\n    },\n    /** 删除按钮操作 */handleDelete: function handleDelete(row) {\n      var _this15 = this;\n      var ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除VIEW编号为\"' + ids + '\"的数据项？').then(function () {\n        return (0, _vw_account_loan.delVw_account_loan)(ids);\n      }).then(function () {\n        _this15.getList();\n        _this15.$modal.msgSuccess('删除成功');\n      }).catch(function () {});\n    },\n    /** 导出按钮操作 */handleExport: function handleExport() {\n      this.download('vw_account_loan/vw_account_loan/export', (0, _objectSpread2.default)({}, this.queryParams), \"vw_account_loan_\".concat(new Date().getTime(), \".xlsx\"));\n    },\n    selectRow: function selectRow() {\n      // Implement the logic for selecting a row\n      console.log('Row selected:', this.vw_account_loanList);\n    },\n    // 通用的上传成功处理函数\n    handleUploadSuccess: function handleUploadSuccess(res, file, fileList, formField) {\n      var _formField$split = formField.split('.'),\n        _formField$split2 = (0, _slicedToArray2.default)(_formField$split, 2),\n        obj = _formField$split2[0],\n        prop = _formField$split2[1];\n      this[obj][prop] = fileList;\n    },\n    // 通用的删除处理函数\n    handleRemove: function handleRemove(file, fileList, formField) {\n      var _formField$split3 = formField.split('.'),\n        _formField$split4 = (0, _slicedToArray2.default)(_formField$split3, 2),\n        obj = _formField$split4[0],\n        prop = _formField$split4[1];\n      this[obj][prop] = fileList;\n    },\n    // 上传失败\n    handleUploadError: function handleUploadError() {\n      this.$modal.msgError('上传图片失败，请重试');\n      this.$modal.closeLoading();\n    },\n    handlePictureCardPreview: function handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url;\n      this.dialogVisible = true;\n    }\n  }\n};", "map": {"version": 3, "names": ["_vw_account_loan", "require", "_auth", "_userInfo", "_interopRequireDefault", "_carInfo", "_loanReminderLog", "_loanReminderLogSubmit", "components", "userInfo", "carInfo", "LoanReminderLog", "LoanReminderLogSubmit", "props", "value", "String", "Object", "Array", "action", "type", "default", "data", "name", "loading", "ids", "single", "multiple", "showSearch", "showMore", "total", "vw_account_loanList", "logopen", "currentRow", "urgeBackopen", "derateopen", "commuteopen", "detailShow", "lenderShow", "customerInfo", "customerId", "applyId", "carShow", "plateNo", "queryParams", "pageNum", "pageSize", "customerName", "certId", "partnerId", "jgName", "slippageStatus", "followStatus", "followUp", "allocationTime", "startTime", "endTime", "isExtension", "car<PERSON>tatus", "isFindCar", "bankList", "label", "isExtensionList", "slippageList", "followUpList", "accountList", "rules", "dialogImageUrl", "dialogVisible", "textarea", "uploadImgUrl", "process", "env", "VUE_APP_BASE_API", "headers", "Authorization", "getToken", "reminderList", "reminderShow", "urgeform", "derateform", "radio", "logList", "logDetail", "logForm", "loanId", "logtotal", "<PERSON><PERSON><PERSON><PERSON>oney", "derate<PERSON>ll<PERSON>oney", "addId", "jmForm", "chForm", "urgeformbtotalMoney", "urgeformdtotalMoney", "urgeformliquidatedDamages", "urgeformoneCommutation", "derateformbtotalMoney", "derateformdtotalMoney", "derateformliquidatedDamages", "derateformoneCommutation", "trialForm", "trialFormprincipal", "trialFormboverdueAmount", "trialForminterest", "trialFormall", "trialFormdoverdueAmount", "trialFormliquidatedDamages", "trialFormtotal", "trialFormotherDebt", "dkyqMoney", "bankyqMoney", "lendingBank", "OrderingChannel", "logLoanId", "submitLoanId", "carStatusList", "isFindCarList", "created", "getList", "getAccountList", "methods", "_this", "get_bank_account", "then", "response", "rows", "handleEstimateBadDebt", "row", "_this2", "id", "badDebt", "update_loan_list", "code", "$modal", "msgSuccess", "msgError", "trialSub", "_this3", "loanAmount", "dc_submit_order", "principal", "defaultInterest", "interest", "btotalMoney", "dtotal<PERSON><PERSON>", "liquidatedDamages", "Number", "toFixed", "submitDerate", "_this4", "accountNumber1", "accountNumber2", "accountNumber3", "otherDebt", "accountNumber4", "accountNumber5", "orgName", "bank", "oneCommutation", "totalMoney", "status", "examineStatus", "trial_balance_put", "submitUrge", "_this5", "onInputLimit", "key", "min", "arguments", "length", "undefined", "max", "val", "isNaN", "cancelDetail", "checkCar", "checkLender", "planBth", "$router", "push", "path", "query", "checkReminder", "_this6", "urgeStatus", "loan_reminder_order", "cancelcommute", "initiate", "_this7", "loan_compensation_order", "contractAmt", "trialBalance", "boverdueAmount", "console", "log", "image", "split", "addTrial", "_this8", "salesman", "nick<PERSON><PERSON>", "add_Trial_order", "cancelderate", "handleInput3", "handleInput2", "handleInput1", "derate<PERSON><PERSON>le", "_this9", "trial_balance_order", "postTrial", "catch", "error", "cancelurgeBack", "get<PERSON><PERSON><PERSON><PERSON>", "e", "_this0", "trial_submit_order", "urgeBackSettle", "_this1", "_this10", "overdueDays", "boverdueDays", "trial_balance_post", "cancelLog", "logView", "$refs", "loanReminderLog", "openLogDialog", "handleSubmitReminder", "_this11", "$nextTick", "loanReminderLogSubmit", "openDialog", "logView2", "_this12", "handleChange", "orgId", "_this13", "$store", "state", "user", "listVw_account_loan", "reset", "form", "doverdueAmount", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "open", "title", "calcMoney", "<PERSON><PERSON><PERSON>", "moneyKey", "submitForm", "_this14", "keys", "reduce", "sum", "$message", "moneyAccountMap", "money", "account", "_i", "_moneyAccountMap", "warning", "concat", "fxjProportion", "fxjMoney", "fxjAccount", "qdProportion", "qdMoney", "qdAccount", "gmjProportion", "gmjMoney", "gmjAccount", "kjjProportion", "kjjMoney", "kjjAccount", "kjczProportion", "kjczMoney", "kjczAccount", "sbczProportion", "sbczMoney", "sbczAccount", "join", "repaymentType", "sub_Trial_order", "handleDelete", "_this15", "confirm", "delVw_account_loan", "handleExport", "download", "_objectSpread2", "Date", "getTime", "selectRow", "handleUploadSuccess", "res", "file", "fileList", "formField", "_formField$split", "_formField$split2", "_slicedToArray2", "obj", "prop", "handleRemove", "_formField$split3", "_formField$split4", "handleUploadError", "closeLoading", "handlePictureCardPreview", "url"], "sources": ["src/views/vw_account_loan/vw_account_loan/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input v-model=\"queryParams.customerName\" placeholder=\"贷款人账户、姓名\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input v-model=\"queryParams.certId\" placeholder=\"贷款人身份证号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"plateNo\">\r\n        <el-input v-model=\"queryParams.plateNo\" placeholder=\"车牌号\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"partnerId\">\r\n        <el-select v-model=\"queryParams.partnerId\" placeholder=\"放款银行\" clearable>\r\n          <el-option v-for=\"dict in bankList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input v-model=\"queryParams.jgName\" placeholder=\"录单渠道名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"followUp\">\r\n        <el-input v-model=\"queryParams.followUp\" placeholder=\"跟催员\" clearable @keyup.enter.native=\"handleQuery\" />\r\n      </el-form-item>\r\n      <template v-if=\"showMore\">\r\n        <el-form-item label=\"\" prop=\"slippageStatus\">\r\n          <el-select v-model=\"queryParams.slippageStatus\" placeholder=\"逾期状态\" clearable>\r\n            <el-option v-for=\"dict in slippageList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"isExtension\">\r\n          <el-select v-model=\"queryParams.isExtension\" placeholder=\"是否延期\" clearable>\r\n            <el-option v-for=\"dict in isExtensionList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"followStatus\">\r\n          <el-select v-model=\"queryParams.followStatus\" placeholder=\"跟催类型\" clearable>\r\n            <el-option v-for=\"dict in followUpList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"扣款时间\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.allocationTime\"\r\n            type=\"daterange\"\r\n            range-separator=\"-\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            value-format=\"yyyy-MM-dd\"></el-date-picker>\r\n        </el-form-item>\r\n      </template>\r\n      <el-form-item label=\"\" prop=\"carStatus\">\r\n        <el-select v-model=\"queryParams.carStatus\" placeholder=\"车辆状态\" clearable>\r\n          <el-option v-for=\"dict in carStatusList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"\" prop=\"isFindCar\">\r\n        <el-select v-model=\"queryParams.isFindCar\" placeholder=\"是否派单找车\" clearable>\r\n          <el-option v-for=\"dict in isFindCarList\" :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item style=\"float: right\">\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n        <el-button type=\"text\" size=\"mini\" @click=\"showMore = !showMore\">\r\n          {{ showMore ? '收起' : '更多' }}\r\n          <i :class=\"showMore ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\r\n        </el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vw_account_loanList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"55\" fixed=\"left\" />\r\n      <el-table-column label=\"逾期状态\" align=\"center\" prop=\"slippageStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.slippageStatus != null\">\r\n            {{\r\n              scope.row.slippageStatus == 1\r\n                ? '提醒'\r\n                : scope.row.slippageStatus == 2\r\n                  ? '电催'\r\n                  : scope.row.slippageStatus == 3\r\n                    ? '上访'\r\n                    : scope.row.slippageStatus == 4\r\n                      ? '逾期30-60'\r\n                      : '逾期60+'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"还款状态\" align=\"center\" prop=\"repaymentStatus\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.repaymentStatus != null\">\r\n            {{\r\n              scope.row.repaymentStatus == 1\r\n                ? '还款中'\r\n                : scope.row.repaymentStatus == 2\r\n                  ? '已完结'\r\n                  : scope.row.repaymentStatus == 3\r\n                    ? '提前结清'\r\n                    : scope.row.repaymentStatus == 4\r\n                      ? '逾期催回结清'\r\n                      : scope.row.repaymentStatus == 5\r\n                        ? '逾期减免结清'\r\n                        : scope.row.repaymentStatus == 6\r\n                          ? '逾期未还款'\r\n                          : scope.row.repaymentStatus == 7\r\n                            ? '逾期还款中'\r\n                            : scope.row.repaymentStatus == 8\r\n                              ? '代偿未还款'\r\n                              : scope.row.repaymentStatus == 9\r\n                                ? '代偿还款中'\r\n                                : scope.row.repaymentStatus == 10\r\n                                  ? '代偿减免结清'\r\n                                  : scope.row.repaymentStatus == 11\r\n                                    ? '代偿全额结清'\r\n                                    : '未知状态'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"延期状态\" align=\"center\" prop=\"isExtension\">\r\n        <template slot-scope=\"scope\">\r\n          <span style=\"color: red\" v-if=\"scope.row.isExtension == 1\">延期</span>\r\n          <span v-else-if=\"scope.row.isExtension == 0\">未延期</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"跟催员\" align=\"center\" prop=\"followUp\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.followUp\">{{ scope.row.followUp }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"贷款人\" align=\"center\" prop=\"customerName\">\r\n        <template slot-scope=\"scope\">\r\n          <span\r\n            v-if=\"scope.row.customerName\"\r\n            style=\"color: #46a6ff; cursor: pointer\"\r\n            @click=\"checkLender({ customerId: scope.row.customerId, applyId: scope.row.applyId })\">\r\n            {{ scope.row.customerName }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"贷款人身份证\" align=\"center\" prop=\"certId\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.certId\">{{ scope.row.certId }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.jgName\">{{ scope.row.jgName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"业务员\" align=\"center\" prop=\"nickName\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.nickName\">{{ scope.row.nickName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"车牌号码\" align=\"center\" prop=\"plateNo\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button v-if=\"scope.row.plateNo\" type=\"text\" @click=\"checkCar(scope.row.plateNo)\">{{ scope.row.plateNo }}</el-button>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"车辆状态\" align=\"center\" prop=\"carStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carStatus != null\">\r\n            {{\r\n              scope.row.carStatus == '1'\r\n                ? '省内正常行驶'\r\n                : scope.row.carStatus == '2'\r\n                  ? '省外正常行驶'\r\n                  : scope.row.carStatus == '3'\r\n                    ? '抵押'\r\n                    : scope.row.carStatus == '4'\r\n                      ? '疑似抵押'\r\n                      : scope.row.carStatus == '5'\r\n                        ? '疑似黑车'\r\n                        : scope.row.carStatus == '6'\r\n                          ? '已入库'\r\n                          : scope.row.carStatus == '7'\r\n                            ? '车在法院'\r\n                            : scope.row.carStatus == '8'\r\n                              ? '已法拍'\r\n                              : '协商卖车'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"车辆位置\" align=\"center\" prop=\"carDetailAddress\" width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carDetailAddress\">{{ scope.row.carDetailAddress }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"GPS状态\" align=\"center\" prop=\"gpsStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.gpsStatus != null\">\r\n            {{\r\n              scope.row.gpsStatus == '1'\r\n                ? '部分拆除'\r\n                : scope.row.gpsStatus == '2'\r\n                  ? '全部拆除'\r\n                  : scope.row.gpsStatus == '3'\r\n                    ? 'GPS正常'\r\n                    : '停车30天以上'\r\n            }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"派车团队\" align=\"center\" prop=\"carTeamName\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.carTeamName\">{{ scope.row.carTeamName }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"bank\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.bank\">{{ scope.row.bank }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"逾期次数\" align=\"center\" prop=\"overdueCount\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.overdueCount != null\">{{ scope.row.overdueCount }}</span>\r\n          <span v-else>0</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"银行逾期天数\" align=\"center\" prop=\"boverdueDays\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.boverdueDays != null\">{{ scope.row.boverdueDays }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"首期逾期金额\" align=\"center\" prop=\"foverdueAmount\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.foverdueAmount != null\">{{ scope.row.foverdueAmount }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"银行逾期金额\" align=\"center\" prop=\"boverdueAmount\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.boverdueAmount != null\">{{ scope.row.boverdueAmount }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"代扣逾期天数\" align=\"center\" prop=\"doverdueDays\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.doverdueDays != null\">{{ scope.row.doverdueDays }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"代扣逾期金额\" align=\"center\" prop=\"doverdueAmount\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.doverdueAmount != null\">{{ scope.row.doverdueAmount }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"催回金额\" align=\"center\" prop=\"realReturnMoney\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.realReturnMoney != null\" style=\"color: #46a6ff; cursor: pointer\" @click=\"checkReminder(scope.row.loanId)\">\r\n            {{ scope.row.realReturnMoney }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"催记类型\" align=\"center\" prop=\"followStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.followStatus != null\">\r\n            {{ scope.row.followStatus == 1 ? '继续联系' : scope.row.followStatus == 2 ? '约定还款' : '无法跟进' }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"催记提交日期\" align=\"center\" prop=\"followDate\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.followDate\">{{ parseTime(scope.row.followDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"下次跟进时间\" align=\"center\" prop=\"trackingTime\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.trackingTime\">\r\n            {{ parseTime(scope.row.trackingTime, '{y}-{m}-{d}') }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"预计还款日期\" align=\"center\" prop=\"appointedTime\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.appointedTime\">\r\n            {{ parseTime(scope.row.appointedTime, '{y}-{m}-{d}') }}\r\n          </span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"催回日期\" align=\"center\" prop=\"reminderDate\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.reminderDate\">{{ parseTime(scope.row.reminderDate, '{y}-{m}-{d}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-popover placement=\"left\" trigger=\"click\" popper-class=\"custom-popover\">\r\n            <div class=\"operation-buttons\">\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleSubmitReminder(scope.row)\"\r\n                v-hasPermi=\"['loan_reminder:loan_reminder:add']\">\r\n                提交催记\r\n              </el-button>\r\n              <el-button class=\"operation-btn\" size=\"mini\" type=\"text\" @click=\"logView(scope.row)\">催记日志</el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"initiate(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:initiate']\">\r\n                发起代偿\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"urgeBackSettle(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:collect']\">\r\n                催回结清\r\n              </el-button>\r\n              <el-button\r\n                class=\"operation-btn\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"derateSettle(scope.row)\"\r\n                v-hasPermi=\"['vw_account_loan:vw_account_loan:derate']\">\r\n                减免结清\r\n              </el-button>\r\n              <el-button class=\"operation-btn\" size=\"mini\" type=\"text\" @click=\"planBth(scope.row)\">还款计划</el-button>\r\n              <el-popconfirm title=\"确认预估呆账吗？\" @confirm=\"handleEstimateBadDebt(scope.row)\">\r\n                <el-button slot=\"reference\" class=\"operation-btn\" size=\"mini\" type=\"text\">预估呆账</el-button>\r\n              </el-popconfirm>\r\n            </div>\r\n            <el-button slot=\"reference\" size=\"mini\" type=\"text\">更多</el-button>\r\n          </el-popover>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n\r\n    <el-dialog :close-on-click-modal=\"false\" class=\"dialogBox\" title=\"催记列表\" :visible.sync=\"reminderShow\" width=\"700px\" append-to-body>\r\n      <el-table :data=\"reminderList\" border style=\"width: 100%\">\r\n        <el-table-column prop=\"identity\" label=\"身份\" width=\"130\">\r\n          <template slot-scope=\"scope\">\r\n            <span>\r\n              {{\r\n                scope.row.identity == 1\r\n                  ? '业务员'\r\n                  : scope.row.identity == 2\r\n                    ? '贷后文员'\r\n                    : scope.row.identity == 3\r\n                      ? '电催员'\r\n                      : scope.row.identity == 4\r\n                        ? '上访员'\r\n                        : scope.row.identity == 5\r\n                          ? '强制上访员'\r\n                          : scope.row.identity == 6\r\n                            ? '找车员'\r\n                            : '法诉文员'\r\n              }}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"customerName\" label=\"催回人\" width=\"120\"></el-table-column>\r\n        <el-table-column prop=\"bmoney\" label=\"银行金额\"></el-table-column>\r\n        <el-table-column prop=\"dmoney\" label=\"代扣金额\"></el-table-column>\r\n        <el-table-column label=\"总金额\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.bmoney + scope.row.dmoney }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"trackingTime\" label=\"催回日期\" width=\"130\"></el-table-column>\r\n        <el-table-column label=\"操作\">\r\n          <template>\r\n            <span style=\"color: #46a6ff; cursor: pointer\">详情</span>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n    <el-dialog :close-on-click-modal=\"false\" class=\"dialogBox\" title=\"发起代偿\" :visible.sync=\"commuteopen\" width=\"900px\" append-to-body>\r\n      <div class=\"settle_money\" @click=\"trialSub\">发起试算</div>\r\n      <el-form ref=\"trialForm\" :model=\"trialForm\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"贷款人\" prop=\"customerName\">\r\n              <el-input v-model=\"trialForm.customerName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出单渠道\" prop=\"orgName\">\r\n              <el-input v-model=\"trialForm.orgName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款银行\" prop=\"bank\">\r\n              <el-input v-model=\"trialForm.bank\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款金额\" prop=\"loanAmount\">\r\n              <el-input v-model=\"trialForm.loanAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"剩余本金\">\r\n              <el-input v-model=\"trialFormprincipal\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行逾期金额\">\r\n              <el-input v-model=\"trialFormboverdueAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行利息\">\r\n              <el-input v-model=\"trialForminterest\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代偿总金额\">\r\n              <el-input v-model=\"trialFormall\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"风险金划比例\" prop=\"fxjProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.fxjProportion\"\r\n                @input=\"onInputLimit('fxjProportion', $event)\"\r\n                @change=\"calcMoney('fxjProportion', 'fxjMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"风险金划金额\" prop=\"fxjMoney\">\r\n              <el-input v-model=\"trialForm.fxjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"fxjAccount\">\r\n              <el-select v-model=\"trialForm.fxjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"渠道转入比例\" prop=\"qdProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.qdProportion\"\r\n                @input=\"onInputLimit('qdProportion', $event)\"\r\n                @change=\"calcMoney('qdProportion', 'qdMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"渠道转入金额\" prop=\"qdMoney\">\r\n              <el-input v-model=\"trialForm.qdMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"qdAccount\">\r\n              <el-select v-model=\"trialForm.qdAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"广明借比例\" prop=\"gmjProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.gmjProportion\"\r\n                @input=\"onInputLimit('gmjProportion', $event)\"\r\n                @change=\"calcMoney('gmjProportion', 'gmjMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"广明借额\" prop=\"gmjMoney\">\r\n              <el-input v-model=\"trialForm.gmjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"gmjAccount\">\r\n              <el-select v-model=\"trialForm.gmjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技借比例\" prop=\"kjczProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.kjjProportion\"\r\n                @input=\"onInputLimit('kjjProportion', $event)\"\r\n                @change=\"calcMoney('kjjProportion', 'kjjMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技借额\" prop=\"kjjMoney\">\r\n              <el-input v-model=\"trialForm.kjjMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"kjjAccount\">\r\n              <el-select v-model=\"trialForm.kjjAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技出资比例\" prop=\"kjczProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.kjczProportion\"\r\n                @input=\"onInputLimit('kjczProportion', $event)\"\r\n                @change=\"calcMoney('kjczProportion', 'kjczMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"科技出资金额\" prop=\"kjczMoney\">\r\n              <el-input v-model=\"trialForm.kjczMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"kjczAccount\">\r\n              <el-select v-model=\"trialForm.kjczAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"守邦出资比例\" prop=\"sbczProportion\">\r\n              <el-input\r\n                v-model.number=\"trialForm.sbczProportion\"\r\n                @input=\"onInputLimit('sbczProportion', $event)\"\r\n                @change=\"calcMoney('sbczProportion', 'sbczMoney', $event)\"\r\n                type=\"number\"\r\n                style=\"width: 100%\">\r\n                <template slot=\"append\">%</template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"守邦出资金额\" prop=\"sbczMoney\">\r\n              <el-input v-model=\"trialForm.sbczMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"账号类型\" prop=\"sbczAccount\">\r\n              <el-select v-model=\"trialForm.sbczAccount\" placeholder=\"账号类型\" clearable>\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.card\" :label=\"dict.name\" :value=\"dict.card\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"上传凭据\">\r\n              <div v-if=\"trialForm.examineStatus >= 1\">\r\n                <template v-if=\"trialForm.image && trialForm.image.length > 0\">\r\n                  <el-image\r\n                    v-for=\"(url, index) in trialForm.image\"\r\n                    :key=\"index\"\r\n                    style=\"width: 100px; height: 100px; margin-right: 10px\"\r\n                    :src=\"url\"\r\n                    fit=\"cover\"\r\n                    :preview-src-list=\"trialForm.image\"></el-image>\r\n                </template>\r\n                <el-upload v-else list-type=\"picture-card\" :disabled=\"true\" :action=\"uploadImgUrl\">\r\n                  <div>暂无凭据</div>\r\n                </el-upload>\r\n              </div>\r\n              <el-upload\r\n                v-else\r\n                :data=\"data\"\r\n                :action=\"uploadImgUrl\"\r\n                list-type=\"picture-card\"\r\n                :headers=\"headers\"\r\n                :file-list=\"trialForm.image\"\r\n                :on-preview=\"handlePictureCardPreview\"\r\n                :on-success=\"(res, file, fileList) => handleUploadSuccess(res, file, fileList, 'trialForm.image')\"\r\n                :on-remove=\"(file, fileList) => handleRemove(file, fileList, 'trialForm.image')\"\r\n                :on-error=\"handleUploadError\">\r\n                <i class=\"el-icon-plus\"></i>\r\n              </el-upload>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代扣剩余未还金额\">\r\n              <el-input v-model=\"trialFormdoverdueAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"违约金\">\r\n              <el-input v-model=\"trialFormliquidatedDamages\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"登记其他欠款金额\" prop=\"otherDebt\">\r\n              <el-input type=\"number\" @input=\"handleInput3\" v-model=\"trialFormotherDebt\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"总账款\">\r\n              <el-input v-model=\"trialFormtotal\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"!trialForm.examineStatus\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancelcommute\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 催回结清对话框 -->\r\n    <el-dialog\r\n      :before-close=\"cancelurgeBack\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"dialogBox\"\r\n      title=\"催回结清\"\r\n      :visible.sync=\"urgeBackopen\"\r\n      width=\"800px\"\r\n      append-to-body>\r\n      <div class=\"settle_money\" @click=\"getUrgeMoney(1)\" v-if=\"urgeform.examineStatus < 1\">获取结清金额</div>\r\n      <el-form ref=\"urgeform\" :model=\"urgeform\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"贷款人\" prop=\"customerName\">\r\n              <el-input v-model=\"urgeform.customerName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出单渠道\" prop=\"orgName\">\r\n              <el-input v-model=\"urgeform.orgName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款银行\" prop=\"bank\">\r\n              <el-input v-model=\"urgeform.bank\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款金额\" prop=\"loanAmount\">\r\n              <el-input v-model=\"urgeform.loanAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行结清金额\" prop=\"btotalMoney\">\r\n              <el-input v-model=\"urgeformbtotalMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber1\">\r\n              <el-select v-model=\"urgeform.accountNumber1\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代扣剩余未还金额\" prop=\"dtotalMoney\">\r\n              <el-input v-model=\"urgeformdtotalMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber2\">\r\n              <el-select v-model=\"urgeform.accountNumber2\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"违约金\" prop=\"liquidatedDamages\">\r\n              <el-input v-model=\"urgeformliquidatedDamages\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber3\">\r\n              <el-select v-model=\"urgeform.accountNumber3\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"其他欠款\" prop=\"otherDebt\">\r\n              <el-input @input=\"handleInput1\" v-model=\"urgeform.otherDebt\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber4\">\r\n              <el-select v-model=\"urgeform.accountNumber4\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"单期代偿金\" prop=\"oneCommutation\">\r\n              <el-input v-model=\"urgeformoneCommutation\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber5\">\r\n              <el-select v-model=\"urgeform.accountNumber5\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"总欠款金额\">\r\n              <el-input v-model=\"urgeAllMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" v-if=\"urgeform.examineStatus <= 1\" @click=\"submitUrge\">确 定</el-button>\r\n        <el-button @click=\"cancelurgeBack\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!-- 减免结清对话框 -->\r\n    <el-dialog\r\n      :before-close=\"cancelderate\"\r\n      :close-on-click-modal=\"false\"\r\n      class=\"dialogBox\"\r\n      title=\"减免结清\"\r\n      :visible.sync=\"derateopen\"\r\n      width=\"800px\"\r\n      append-to-body>\r\n      <div class=\"settle_money\" @click=\"getUrgeMoney(2)\" v-if=\"urgeform.examineStatus < 1\">获取结清金额</div>\r\n      <el-form ref=\"derateform\" :model=\"derateform\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"贷款人\" prop=\"customerName\">\r\n              <el-input v-model=\"derateform.customerName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"出单渠道\" prop=\"orgName\">\r\n              <el-input v-model=\"derateform.orgName\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款银行\" prop=\"bank\">\r\n              <el-input v-model=\"derateform.bank\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"放款金额\" prop=\"loanAmount\">\r\n              <el-input v-model=\"derateform.loanAmount\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"银行结清金额\" prop=\"btotalMoney\">\r\n              <el-input v-model=\"derateformbtotalMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber1\">\r\n              <el-select v-model=\"derateform.accountNumber1\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"代扣剩余未还金额\" prop=\"dtotalMoney\">\r\n              <el-input v-model=\"derateformdtotalMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber2\">\r\n              <el-select v-model=\"derateform.accountNumber2\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"违约金\" prop=\"liquidatedDamages\">\r\n              <el-input v-model=\"derateformliquidatedDamages\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber3\">\r\n              <el-select v-model=\"derateform.accountNumber3\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"其他欠款\" prop=\"otherDebt\">\r\n              <el-input @input=\"handleInput2\" v-model=\"derateform.otherDebt\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber4\">\r\n              <el-select v-model=\"derateform.accountNumber4\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"单期代偿金\" prop=\"oneCommutation\">\r\n              <el-input v-model=\"derateformoneCommutation\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"账号类型\" prop=\"accountNumber5\">\r\n              <el-select v-model=\"derateform.accountNumber5\" placeholder=\"账号类型\">\r\n                <el-option v-for=\"dict in accountList\" :key=\"dict.name\" :label=\"dict.name\" :value=\"dict.name\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"总欠款金额\">\r\n              <el-input v-model=\"derateAllMoney\" :disabled=\"true\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" v-if=\"derateform.examineStatus <= 1\" @click=\"submitDerate\">确 定</el-button>\r\n        <el-button @click=\"cancelderate\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"lenderShow\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n    <car-info ref=\"carInfo\" :visible.sync=\"carShow\" title=\"车辆信息\" :plateNo=\"plateNo\" permission=\"1\" />\r\n    <!-- 催记日志组件 -->\r\n    <loan-reminder-log ref=\"loanReminderLog\" :loan-id=\"logLoanId\" />\r\n    <!-- 提交催记组件 -->\r\n    <loan-reminder-log-submit ref=\"loanReminderLogSubmit\" :loan-id=\"submitLoanId\" :status=\"3\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  trial_balance_put,\r\n  trial_submit_order,\r\n  trial_balance_post,\r\n  trial_balance_order,\r\n  loan_reminder_order,\r\n  listVw_account_loan,\r\n  delVw_account_loan,\r\n  loan_compensation_order,\r\n  add_Trial_order,\r\n  dc_submit_order,\r\n  sub_Trial_order,\r\n  update_loan_list,\r\n  get_bank_account,\r\n} from '@/api/vw_account_loan/vw_account_loan'\r\nimport { getToken } from '@/utils/auth'\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nimport carInfo from '@/layout/components/Dialog/carInfo.vue'\r\nimport LoanReminderLog from '@/layout/components/Dialog/loanReminderLog.vue'\r\nimport LoanReminderLogSubmit from '@/layout/components/Dialog/loanReminderLogSubmit.vue'\r\n\r\nexport default {\r\n  components: {\r\n    userInfo,\r\n    carInfo,\r\n    LoanReminderLog,\r\n    LoanReminderLogSubmit,\r\n  },\r\n  props: {\r\n    value: [String, Object, Array],\r\n    // 上传接口地址\r\n    action: {\r\n      type: String,\r\n      // default: \"/common/upload\"\r\n      default: '/common/ossupload',\r\n    },\r\n    // 上传携带的参数\r\n    data: {\r\n      type: Object,\r\n    },\r\n  },\r\n  name: 'Vw_account_loan',\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 控制更多筛选条件显示\r\n      showMore: false,\r\n      // 总条数\r\n      total: 0,\r\n      // VIEW表格数据\r\n      vw_account_loanList: [],\r\n      // 是否显示弹出层\r\n      logopen: false, //催记日志\r\n      currentRow: {}, //dialog传入数据\r\n      urgeBackopen: false, //催回结清\r\n      derateopen: false, //减免结清\r\n      commuteopen: false, //发起代偿\r\n      detailShow: false, //查看日志详情\r\n      lenderShow: false, //贷款人信息\r\n      customerInfo: {\r\n        customerId: '',\r\n        applyId: '',\r\n      },\r\n      carShow: false, // 车辆信息\r\n      plateNo: '', // 车辆编号\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n        customerName: null,\r\n        certId: null,\r\n        plateNo: null,\r\n        partnerId: null,\r\n        jgName: null,\r\n        slippageStatus: null,\r\n        followStatus: null,\r\n        followUp: null,\r\n        allocationTime: null,\r\n        startTime: '',\r\n        endTime: '',\r\n        isExtension: '',\r\n        carStatus: '',\r\n        isFindCar: '',\r\n      },\r\n\r\n      bankList: [\r\n        { value: 'EO00000010', label: '苏银金租' },\r\n        { value: 'IO00000006', label: '浙商银行' },\r\n        { value: 'IO00000007', label: '中关村银行' },\r\n        { value: 'IO00000008', label: '蓝海银行' },\r\n        { value: 'IO00000009', label: '华瑞银行' },\r\n        { value: 'IO00000010', label: '皖新租赁' },\r\n      ],\r\n\r\n      isExtensionList: [\r\n        { label: '否', value: '0' },\r\n        { label: '是', value: '1' },\r\n      ],\r\n      slippageList: [\r\n        { label: '提醒', value: 1 },\r\n        { label: '电催', value: 2 },\r\n        { label: '上访', value: 3 },\r\n        { label: '逾期30-60', value: 4 },\r\n        { label: '逾期60+', value: 5 },\r\n      ],\r\n      followUpList: [\r\n        { label: '继续联系', value: 1 },\r\n        { label: '约定还款', value: 2 },\r\n        { label: '无法跟进', value: 3 },\r\n      ],\r\n      accountList: [], //银行账户列表\r\n      // 表单校验\r\n      rules: {},\r\n      dialogImageUrl: '',\r\n      dialogVisible: false,\r\n      textarea: null,\r\n      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action, // 上传的图片服务器地址\r\n      headers: {\r\n        Authorization: 'Bearer ' + getToken(),\r\n      },\r\n      reminderList: [], //催记列表\r\n      reminderShow: false,\r\n      urgeform: {},\r\n      derateform: {},\r\n      radio: 0,\r\n      logList: [], //催记日志\r\n      logDetail: {}, //催记日志详情\r\n      logForm: {\r\n        loanId: null,\r\n        pageNum: 1,\r\n        pageSize: 15,\r\n      },\r\n      logtotal: 0,\r\n      urgeAllMoney: 0, //催回结清总欠款\r\n      derateAllMoney: 0, //减免结清总欠款\r\n      loanId: null,\r\n      addId: null, //催回、减免新增id\r\n      jmForm: {},\r\n      chForm: {},\r\n      urgeformbtotalMoney: 0,\r\n      urgeformdtotalMoney: 0,\r\n      urgeformliquidatedDamages: 0,\r\n      urgeformoneCommutation: 0,\r\n      derateformbtotalMoney: 0,\r\n      derateformdtotalMoney: 0,\r\n      derateformliquidatedDamages: 0,\r\n      derateformoneCommutation: 0,\r\n      trialForm: {},\r\n      trialFormprincipal: 0,\r\n      trialFormboverdueAmount: 0,\r\n      trialForminterest: 0,\r\n      trialFormall: 0,\r\n      trialFormdoverdueAmount: 0,\r\n      trialFormliquidatedDamages: 0,\r\n      trialFormtotal: 0,\r\n      trialFormotherDebt: 0,\r\n      dkyqMoney: 0,\r\n      bankyqMoney: 0,\r\n      lendingBank: null,\r\n      OrderingChannel: null,\r\n      logLoanId: null, //嵌入催记日志组件的loanId\r\n      submitLoanId: null, //嵌入提交催记组件的loanId\r\n      carStatusList: [\r\n        { label: '省内正常行驶', value: '1' },\r\n        { label: '省外正常行驶', value: '2' },\r\n        { label: '抵押', value: '3' },\r\n        { label: '疑似抵押', value: '4' },\r\n        { label: '疑似黑车', value: '5' },\r\n        { label: '已入库', value: '6' },\r\n        { label: '车在法院', value: '7' },\r\n        { label: '已法拍', value: '8' },\r\n        { label: '协商卖车', value: '9' },\r\n      ],\r\n      isFindCarList: [\r\n        { label: '未派单', value: '0' },\r\n        { label: '已派单', value: '1' },\r\n      ],\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getAccountList()\r\n  },\r\n  methods: {\r\n    getAccountList() {\r\n      get_bank_account().then(response => {\r\n        this.accountList = response.rows\r\n      })\r\n    },\r\n    handleEstimateBadDebt(row) {\r\n      const data = {\r\n        id: row.loanId,\r\n        badDebt: 1,\r\n      }\r\n      update_loan_list(data).then(response => {\r\n        if (response.code == 200) {\r\n          this.$modal.msgSuccess('预估呆账成功')\r\n        } else {\r\n          this.$modal.msgError('预估呆账失败')\r\n        }\r\n      })\r\n    },\r\n    trialSub() {\r\n      var data = {\r\n        applyId: this.trialForm.applyId,\r\n        id: this.trialForm.id,\r\n        loanId: this.trialForm.loanId,\r\n        loanAmount: this.trialForm.loanAmount,\r\n        partnerId: this.trialForm.partnerId,\r\n      }\r\n      dc_submit_order(data).then(response => {\r\n        this.trialFormprincipal = response.data.principal || 0\r\n        this.trialForm.defaultInterest = response.data.defaultInterest || 0\r\n        this.trialForminterest = response.data.interest || 0\r\n        this.trialFormall = response.data.btotalMoney || 0\r\n        this.trialFormdoverdueAmount = response.data.dtotalMoney || 0\r\n        this.trialFormliquidatedDamages = response.data.liquidatedDamages || 0\r\n        this.trialFormtotal = Number(\r\n          this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n        ).toFixed(2)\r\n      })\r\n    },\r\n    submitDerate() {\r\n      if (this.derateformbtotalMoney && !this.derateform.accountNumber1) {\r\n        this.$modal.msgError('请选择银行结清账号')\r\n        return\r\n      }\r\n      if (this.derateformdtotalMoney && !this.derateform.accountNumber2) {\r\n        this.$modal.msgError('请选择代扣剩余账号')\r\n        return\r\n      }\r\n      if (this.derateformliquidatedDamages && !this.derateform.accountNumber3) {\r\n        this.$modal.msgError('请选择违约金账号')\r\n        return\r\n      }\r\n      if (this.derateform.otherDebt && !this.derateform.accountNumber4) {\r\n        this.$modal.msgError('请选择其他欠款账号')\r\n        return\r\n      }\r\n      if (this.derateformoneCommutation && !this.derateform.accountNumber5) {\r\n        this.$modal.msgError('请选择单期代偿金账号')\r\n        return\r\n      }\r\n      var data = {\r\n        customerName: this.derateform.customerName,\r\n        orgName: this.derateform.orgName,\r\n        bank: this.derateform.bank,\r\n        loanAmount: this.derateform.loanAmount,\r\n        btotalMoney: this.derateform.btotalMoney,\r\n        accountNumber1: this.derateform.accountNumber1,\r\n        dtotalMoney: this.derateform.dtotalMoney,\r\n        accountNumber2: this.derateform.accountNumber2,\r\n        liquidatedDamages: this.derateform.liquidatedDamages,\r\n        accountNumber3: this.derateform.accountNumber3,\r\n        otherDebt: this.derateform.otherDebt,\r\n        accountNumber4: this.derateform.accountNumber4,\r\n        oneCommutation: this.derateform.oneCommutation,\r\n        accountNumber5: this.derateform.accountNumber5,\r\n        totalMoney: this.derateAllMoney,\r\n        id: this.addId,\r\n        status: 2,\r\n        examineStatus: 1,\r\n      }\r\n      trial_balance_put(data).then(response => {\r\n        if (response.code == 200) {\r\n          this.$modal.msgSuccess('提交成功')\r\n          this.derateopen = false\r\n          this.derateform = {}\r\n          this.derateformbtotalMoney = 0\r\n          this.derateformdtotalMoney = 0\r\n          this.derateformliquidatedDamages = 0\r\n          this.derateformoneCommutation = 0\r\n        }\r\n      })\r\n    },\r\n    submitUrge() {\r\n      if (this.urgeformbtotalMoney && !this.urgeform.accountNumber1) {\r\n        this.$modal.msgError('请选择银行结清账号')\r\n        return\r\n      }\r\n      if (this.urgeformdtotalMoney && !this.urgeform.accountNumber2) {\r\n        this.$modal.msgError('请选择代扣剩余账号')\r\n        return\r\n      }\r\n      if (this.urgeformliquidatedDamages && !this.urgeform.accountNumber3) {\r\n        this.$modal.msgError('请选择违约金账号')\r\n        return\r\n      }\r\n      if (this.urgeform.otherDebt && !this.urgeform.accountNumber4) {\r\n        this.$modal.msgError('请选择其他欠款账号')\r\n        return\r\n      }\r\n      if (this.urgeformoneCommutation && !this.urgeform.accountNumber5) {\r\n        this.$modal.msgError('请选择单期代偿金账号')\r\n        return\r\n      }\r\n      var data = {\r\n        customerName: this.urgeform.customerName,\r\n        orgName: this.urgeform.orgName,\r\n        bank: this.urgeform.bank,\r\n        loanAmount: this.urgeform.loanAmount,\r\n        btotalMoney: this.urgeform.btotalMoney,\r\n        accountNumber1: this.urgeform.accountNumber1,\r\n        dtotalMoney: this.urgeform.dtotalMoney,\r\n        accountNumber2: this.urgeform.accountNumber2,\r\n        liquidatedDamages: this.urgeform.liquidatedDamages,\r\n        accountNumber3: this.urgeform.accountNumber3,\r\n        otherDebt: this.urgeform.otherDebt,\r\n        accountNumber4: this.urgeform.accountNumber4,\r\n        oneCommutation: this.urgeform.oneCommutation,\r\n        accountNumber5: this.urgeform.accountNumber5,\r\n        totalMoney: this.urgeAllMoney,\r\n        id: this.addId,\r\n        status: 1,\r\n        examineStatus: 1,\r\n      }\r\n      trial_balance_put(data).then(response => {\r\n        if (response.code == 200) {\r\n          this.$modal.msgSuccess('提交成功')\r\n          this.urgeBackopen = false\r\n          this.urgeform = {}\r\n          this.urgeformbtotalMoney = 0\r\n          this.urgeformdtotalMoney = 0\r\n          this.urgeformliquidatedDamages = 0\r\n          this.urgeformoneCommutation = 0\r\n        }\r\n      })\r\n    },\r\n    onInputLimit(key, value, min = 0, max = 100) {\r\n      let val = Number(value)\r\n      if (isNaN(val)) val = min\r\n      if (val > max) val = max\r\n      if (val < min) val = min\r\n      this.trialForm[key] = Number(val)\r\n    },\r\n    cancelDetail() {\r\n      this.detailShow = false\r\n    },\r\n    //查看车牌信息\r\n    checkCar(plateNo) {\r\n      this.plateNo = plateNo\r\n      this.carShow = true\r\n    },\r\n    //查看贷款人信息\r\n    checkLender(customerInfo) {\r\n      this.customerInfo = customerInfo\r\n      this.lenderShow = true\r\n    },\r\n    planBth(row) {\r\n      var data = {\r\n        partnerId: row.partnerId,\r\n        applyId: row.applyId,\r\n      }\r\n      if (row.partnerId == 'IO00000008') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/lhindex',\r\n          query: data,\r\n        })\r\n      } else if (row.partnerId == 'EO00000010') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/syindex',\r\n          query: data,\r\n        })\r\n      } else if (row.partnerId == 'IO00000006') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/zsindex',\r\n          query: data,\r\n        })\r\n      } else if (row.partnerId == 'IO00000007') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/zgcindex',\r\n          query: data,\r\n        })\r\n      } else if (row.partnerId == 'IO00000009') {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/hrindex',\r\n          query: data,\r\n        })\r\n      } else {\r\n        this.$router.push({\r\n          path: '/repayment/repayment_plan/wxindex',\r\n          query: data,\r\n        })\r\n      }\r\n    },\r\n    checkReminder(id) {\r\n      var data = {\r\n        loanId: id,\r\n        urgeStatus: 2,\r\n      }\r\n      loan_reminder_order(data).then(response => {\r\n        this.reminderList = response.rows\r\n        this.reminderShow = true\r\n      })\r\n    },\r\n\r\n    cancelcommute() {\r\n      this.commuteopen = false\r\n    },\r\n    initiate(row) {\r\n      //发起代偿\r\n      var data = {\r\n        loanId: row.loanId,\r\n      }\r\n      loan_compensation_order(data).then(response => {\r\n        if (response.data) {\r\n          this.trialForm = response.data\r\n          this.trialForm.loanAmount = row.contractAmt || 0\r\n          this.trialForm.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n          this.trialFormprincipal = response.data.trialBalance ? response.data.trialBalance.principal : 0\r\n          this.trialFormboverdueAmount = row.boverdueAmount || 0\r\n          this.trialForminterest = response.data.trialBalance ? response.data.trialBalance.interest : 0\r\n          this.trialFormall = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n          this.trialFormdoverdueAmount = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n          this.trialFormliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n          this.trialFormotherDebt = response.data.otherDebt ? response.data.otherDebt : 0\r\n          console.log(this.trialFormall, this.trialFormdoverdueAmount, this.trialFormliquidatedDamages, this.trialFormotherDebt)\r\n          this.trialFormtotal = Number(\r\n            this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n          ).toFixed(2)\r\n          this.trialForm.image = response.data.image ? response.data.image.split(',') : []\r\n\r\n          this.commuteopen = true\r\n        } else {\r\n          this.addTrial(row)\r\n          this.trialForm.image = []\r\n        }\r\n      })\r\n    },\r\n    addTrial(row) {\r\n      console.log(row)\r\n      var data = {\r\n        id: row.id,\r\n        applyId: row.applyId,\r\n        loanId: row.loanId,\r\n        customerId: row.customerId,\r\n        customerName: row.customerName,\r\n        salesman: row.nickName,\r\n        orgName: row.jgName,\r\n        partnerId: row.partnerId,\r\n        bank: row.orgName,\r\n        loanAmount: row.contractAmt,\r\n        examineStatus: 0,\r\n      }\r\n      add_Trial_order(data).then(response => {\r\n        this.trialForm = response.data || {}\r\n        this.commuteopen = true\r\n      })\r\n    },\r\n    cancelderate() {\r\n      this.derateform = {}\r\n      this.jmForm = {}\r\n      this.derateformbtotalMoney = 0\r\n      this.derateformdtotalMoney = 0\r\n      this.derateformliquidatedDamages = 0\r\n      this.derateformoneCommutation = 0\r\n      this.derateAllMoney = 0\r\n      this.derateopen = false\r\n    },\r\n    handleInput3(value) {\r\n      this.trialFormotherDebt = Number(value)\r\n      this.trialFormtotal = Number(\r\n        this.trialFormall + this.trialFormdoverdueAmount + this.trialFormliquidatedDamages + this.trialFormotherDebt\r\n      ).toFixed(2)\r\n    },\r\n    handleInput2(value) {\r\n      this.derateform.otherDebt = Number(value)\r\n      this.derateAllMoney = Number(\r\n        this.derateform.btotalMoney +\r\n          this.derateform.dtotalMoney +\r\n          this.derateform.liquidatedDamages +\r\n          this.derateform.otherDebt +\r\n          this.derateform.oneCommutation\r\n      ).toFixed(2)\r\n    },\r\n    handleInput1(value) {\r\n      this.urgeform.otherDebt = Number(value)\r\n      this.urgeAllMoney = Number(\r\n        this.urgeform.btotalMoney +\r\n          this.urgeform.dtotalMoney +\r\n          this.urgeform.liquidatedDamages +\r\n          this.urgeform.otherDebt +\r\n          this.urgeform.oneCommutation\r\n      ).toFixed(2)\r\n    },\r\n    derateSettle(row) {\r\n      var data = {\r\n        loanId: row.loanId,\r\n        status: 2,\r\n        pageSize: 1,\r\n        pageNum: 1,\r\n      }\r\n      this.loanId = row.loanId\r\n      trial_balance_order(data)\r\n        .then(response => {\r\n          if (response.data) {\r\n            this.derateform = response.data\r\n            if (!response.data.customerName) {\r\n              this.derateform.customerName = row.customerName\r\n            }\r\n            if (!response.data.orgName) {\r\n              this.derateform.orgName = row.jgName\r\n            }\r\n            if (!response.data.bank) {\r\n              this.derateform.bank = row.orgName\r\n            }\r\n            if (!response.data.loanAmount) {\r\n              this.derateform.loanAmount = row.contractAmt\r\n            }\r\n            this.derateform.btotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n            this.derateform.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n            this.derateform.oneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0\r\n            this.derateform.liquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n            this.derateformbtotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n            this.derateformdtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n            this.derateformoneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0\r\n            this.derateformliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n            this.derateAllMoney = Number(\r\n              this.derateform.btotalMoney + this.derateform.dtotalMoney + this.derateform.liquidatedDamages + this.derateform.oneCommutation\r\n            ).toFixed(2)\r\n            this.addId = response.data.id\r\n            this.jmForm = response.data\r\n          } else {\r\n            this.postTrial(row, 2)\r\n            // this.derateform = {}\r\n            // this.derateform.customerName = row.customerName\r\n            // this.derateform.orgName = row.jgName\r\n            // this.derateform.bank = row.orgName\r\n            // this.derateform.loanAmount = row.contractAmt\r\n          }\r\n        })\r\n        .catch(error => {})\r\n      this.derateopen = true\r\n    },\r\n    cancelurgeBack() {\r\n      this.urgeform = {}\r\n      this.chForm = {}\r\n      this.urgeformbtotalMoney = 0\r\n      this.urgeformdtotalMoney = 0\r\n      this.urgeformliquidatedDamages = 0\r\n      this.urgeformoneCommutation = 0\r\n      this.urgeAllMoney = 0\r\n      this.urgeBackopen = false\r\n    },\r\n    getUrgeMoney(e) {\r\n      var data = {}\r\n      if (e == 1) {\r\n        data = this.chForm\r\n      } else {\r\n        data = this.jmForm\r\n      }\r\n      trial_submit_order(data).then(response => {\r\n        if (e == 1) {\r\n          // this.urgeform = response.data\r\n          this.urgeform.btotalMoney = response.data.btotalMoney || 0\r\n          this.urgeform.dtotalMoney = response.data.dtotalMoney || 0\r\n          this.urgeform.liquidatedDamages = response.data.liquidatedDamages || 0\r\n          this.urgeform.oneCommutation = response.data.oneCommutation || 0\r\n          this.urgeformbtotalMoney = response.data.btotalMoney || 0\r\n          this.urgeformdtotalMoney = response.data.dtotalMoney || 0\r\n          this.urgeformliquidatedDamages = response.data.liquidatedDamages || 0\r\n          this.urgeformoneCommutation = response.data.oneCommutation || 0\r\n          this.urgeAllMoney = Number(\r\n            this.urgeform.btotalMoney +\r\n              this.urgeform.dtotalMoney +\r\n              this.urgeform.liquidatedDamages +\r\n              this.urgeform.otherDebt +\r\n              this.urgeform.oneCommutation\r\n          ).toFixed(2)\r\n        } else {\r\n          // this.derateform = response.data\r\n          this.derateform.btotalMoney = response.data.btotalMoney || 0\r\n          this.derateform.dtotalMoney = response.data.dtotalMoney || 0\r\n          this.derateform.liquidatedDamages = response.data.liquidatedDamages || 0\r\n          this.derateform.oneCommutation = response.data.oneCommutation || 0\r\n          this.derateformbtotalMoney = response.data.btotalMoney || 0\r\n          this.derateformdtotalMoney = response.data.dtotalMoney || 0\r\n          this.derateformliquidatedDamages = response.data.liquidatedDamages || 0\r\n          this.derateformoneCommutation = response.data.oneCommutation || 0\r\n          this.derateAllMoney = Number(\r\n            this.derateform.btotalMoney +\r\n              this.derateform.dtotalMoney +\r\n              this.derateform.liquidatedDamages +\r\n              this.derateform.otherDebt +\r\n              this.derateform.oneCommutation\r\n          ).toFixed(2)\r\n        }\r\n      })\r\n    },\r\n    urgeBackSettle(row) {\r\n      var data = {\r\n        loanId: row.loanId,\r\n        status: 1,\r\n        pageSize: 1,\r\n        pageNum: 1,\r\n      }\r\n      this.loanId = row.loanId\r\n      trial_balance_order(data)\r\n        .then(response => {\r\n          if (response.data) {\r\n            this.urgeform = response.data\r\n            if (!response.data.customerName) {\r\n              this.urgeform.customerName = row.customerName\r\n            }\r\n            if (!response.data.orgName) {\r\n              this.urgeform.orgName = row.jgName\r\n            }\r\n            if (!response.data.bank) {\r\n              this.urgeform.bank = row.orgName\r\n            }\r\n            if (!response.data.loanAmount) {\r\n              this.urgeform.loanAmount = row.contractAmt\r\n            }\r\n            this.urgeform.btotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n            this.urgeform.dtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n            this.urgeform.liquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n            this.urgeform.oneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0\r\n            this.urgeformbtotalMoney = response.data.trialBalance ? response.data.trialBalance.btotalMoney : 0\r\n            this.urgeformdtotalMoney = response.data.trialBalance ? response.data.trialBalance.dtotalMoney : 0\r\n            this.urgeformliquidatedDamages = response.data.trialBalance ? response.data.trialBalance.liquidatedDamages : 0\r\n            this.urgeformoneCommutation = response.data.trialBalance ? response.data.trialBalance.oneCommutation : 0\r\n            this.urgeAllMoney = Number(\r\n              this.urgeform.btotalMoney + this.urgeform.dtotalMoney + this.urgeform.liquidatedDamages + this.urgeform.oneCommutation\r\n            ).toFixed(2)\r\n            this.addId = response.data.id\r\n            this.chForm = response.data\r\n          } else {\r\n            this.postTrial(row, 1)\r\n            // this.urgeform = {}\r\n            // this.urgeform.customerName = row.customerName\r\n            // this.urgeform.orgName = row.jgName\r\n            // this.urgeform.bank = row.orgName\r\n            // this.urgeform.loanAmount = row.contractAmt\r\n          }\r\n          this.urgeBackopen = true\r\n        })\r\n        .catch(error => {})\r\n    },\r\n    postTrial(row, e) {\r\n      var data = {\r\n        applyId: row.applyId,\r\n        loanId: row.loanId,\r\n        customerName: row.customerName,\r\n        orgName: row.jgName,\r\n        loanAmount: row.contractAmt,\r\n        bank: row.orgName,\r\n        partnerId: row.partnerId,\r\n        status: e,\r\n        salesman: row.nickName,\r\n        overdueDays: row.boverdueDays,\r\n        examineStatus: 0,\r\n      }\r\n      trial_balance_post(data).then(response => {\r\n        this.addId = response.id\r\n        if (e == 1) {\r\n          this.urgeform = response.data\r\n          this.chForm = response.data\r\n        } else {\r\n          this.derateform = response.data\r\n          this.jmForm = response.data\r\n        }\r\n      })\r\n    },\r\n    cancelLog() {\r\n      this.logopen = false\r\n    },\r\n    logView(row) {\r\n      this.logLoanId = row.loanId\r\n      this.$refs.loanReminderLog.openLogDialog()\r\n    },\r\n    handleSubmitReminder(row) {\r\n      this.submitLoanId = row.loanId\r\n      this.$nextTick(() => {\r\n        this.$refs.loanReminderLogSubmit.openDialog()\r\n      })\r\n    },\r\n    logView2() {\r\n      loan_reminder_order(this.logForm)\r\n        .then(response => {\r\n          this.logList = response.rows\r\n          this.logtotal = response.total\r\n          this.logopen = true\r\n        })\r\n        .catch(error => {})\r\n    },\r\n\r\n    handleChange(value) {\r\n      this.queryParams.orgId = value\r\n    },\r\n    /** 查询VIEW列表 */\r\n    getList() {\r\n      console.log(this.$store.state.user)\r\n      this.loading = true\r\n      listVw_account_loan(this.queryParams).then(response => {\r\n        this.vw_account_loanList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        customerName: null,\r\n        jgName: null,\r\n        orgName: null,\r\n        boverdueAmount: null,\r\n        doverdueAmount: null,\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      if (this.queryParams.allocationTime) {\r\n        this.queryParams.startTime = this.queryParams.allocationTime[0]\r\n        this.queryParams.endTime = this.queryParams.allocationTime[1]\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams.customerName = null\r\n      this.queryParams.certId = null\r\n      this.queryParams.plateNo = null\r\n      this.queryParams.partnerId = null\r\n      this.queryParams.jgName = null\r\n      this.queryParams.slippageStatus = null\r\n      this.queryParams.followStatus = null\r\n      this.queryParams.followUp = null\r\n      this.queryParams.allocationTime = null\r\n      this.queryParams.startTime = null\r\n      this.queryParams.endTime = null\r\n      this.queryParams.isExtension = null\r\n      this.queryParams.carStatus = ''\r\n      this.queryParams.isFindCar = ''\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = '添加VIEW'\r\n    },\r\n\r\n    calcMoney(proportionKey, moneyKey, val) {\r\n      // 只做金额计算和保留两位小数\r\n      this.trialForm[moneyKey] = ((val * this.trialFormall) / 100).toFixed(2)\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const keys = ['fxjProportion', 'qdProportion', 'gmjProportion', 'kjjProportion', 'kjczProportion', 'sbczProportion']\r\n      // 计算比例之和\r\n      const total = keys.reduce((sum, key) => sum + Number(this.trialForm[key] || 0), 0)\r\n      if (total !== 100) {\r\n        this.$message.error('六项比例之和必须等于100！')\r\n        return\r\n      }\r\n\r\n      // 金额和账号类型字段映射\r\n      const moneyAccountMap = [\r\n        { money: 'fxjMoney', account: 'fxjAccount', label: '风险金账号类型' },\r\n        { money: 'qdMoney', account: 'qdAccount', label: '渠道账号类型' },\r\n        { money: 'gmjMoney', account: 'gmjAccount', label: '广明借账号类型' },\r\n        { money: 'kjjMoney', account: 'kjjAccount', label: '科技借账号类型' },\r\n        {\r\n          money: 'kjczMoney',\r\n          account: 'kjczAccount',\r\n          label: '科技出资账号类型',\r\n        },\r\n        {\r\n          money: 'sbczMoney',\r\n          account: 'sbczAccount',\r\n          label: '守邦出资账号类型',\r\n        },\r\n      ]\r\n\r\n      // 校验每个金额>0时账号类型必填\r\n      for (const item of moneyAccountMap) {\r\n        if (Number(this.trialForm[item.money]) > 0 && !this.trialForm[item.account]) {\r\n          this.$message.warning(`请先选择${item.label}`)\r\n          return\r\n        }\r\n      }\r\n      var data = {\r\n        id: this.trialForm.id,\r\n        fxjProportion: this.trialForm.fxjProportion,\r\n        fxjMoney: this.trialForm.fxjMoney,\r\n        fxjAccount: this.trialForm.fxjAccount,\r\n        qdProportion: this.trialForm.qdProportion,\r\n        qdMoney: this.trialForm.qdMoney,\r\n        qdAccount: this.trialForm.qdAccount,\r\n        gmjProportion: this.trialForm.gmjProportion,\r\n        gmjMoney: this.trialForm.gmjMoney,\r\n        gmjAccount: this.trialForm.gmjAccount,\r\n        kjjProportion: this.trialForm.kjjProportion,\r\n        kjjMoney: this.trialForm.kjjMoney,\r\n        kjjAccount: this.trialForm.kjjAccount,\r\n        kjczProportion: this.trialForm.kjczProportion,\r\n        kjczMoney: this.trialForm.kjczMoney,\r\n        kjczAccount: this.trialForm.kjczAccount,\r\n        sbczProportion: this.trialForm.sbczProportion,\r\n        sbczMoney: this.trialForm.sbczMoney,\r\n        sbczAccount: this.trialForm.sbczAccount,\r\n        otherDebt: this.trialFormotherDebt,\r\n        totalMoney: this.trialFormtotal,\r\n        image: this.trialForm.image.map(item => item.response).join(','),\r\n        examineStatus: 1,\r\n        repaymentType: 0,\r\n      }\r\n      console.log(data)\r\n      sub_Trial_order(data).then(response => {\r\n        if (response.code == 200) {\r\n          this.$modal.msgSuccess('提交成功')\r\n          this.commuteopen = false\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal\r\n        .confirm('是否确认删除VIEW编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delVw_account_loan(ids)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('删除成功')\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'vw_account_loan/vw_account_loan/export',\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `vw_account_loan_${new Date().getTime()}.xlsx`\r\n      )\r\n    },\r\n    selectRow() {\r\n      // Implement the logic for selecting a row\r\n      console.log('Row selected:', this.vw_account_loanList)\r\n    },\r\n\r\n    // 通用的上传成功处理函数\r\n    handleUploadSuccess(res, file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 通用的删除处理函数\r\n    handleRemove(file, fileList, formField) {\r\n      const [obj, prop] = formField.split('.')\r\n      this[obj][prop] = fileList\r\n    },\r\n    // 上传失败\r\n    handleUploadError() {\r\n      this.$modal.msgError('上传图片失败，请重试')\r\n      this.$modal.closeLoading()\r\n    },\r\n    handlePictureCardPreview(file) {\r\n      this.dialogImageUrl = file.url\r\n      this.dialogVisible = true\r\n    },\r\n  },\r\n}\r\n</script>\r\n<style>\r\n.dialogBox .el-form-item__label {\r\n  width: 100px !important;\r\n}\r\n\r\n.dialogBox .el-form-item__content {\r\n  margin-left: 100px !important;\r\n}\r\n\r\n.settle_money {\r\n  background-color: #1890ff;\r\n  color: #fff;\r\n  border-radius: 5px;\r\n  display: inline-block;\r\n  padding: 5px 10px;\r\n  box-sizing: border-box;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.el-dialog__body {\r\n  padding-top: 10px !important;\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* New styles for the filter bar */\r\n.filter-container {\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  width: 100%;\r\n  margin-bottom: 10px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.filter-item-wrapper {\r\n  flex: 1;\r\n  min-width: 180px;\r\n  padding: 0 5px;\r\n}\r\n\r\n.filter-item-wrapper .el-form-item {\r\n  margin-bottom: 0;\r\n  width: 100%;\r\n}\r\n\r\n.filter-item-wrapper .el-select,\r\n.filter-item-wrapper .el-input,\r\n.filter-item-wrapper .el-date-editor {\r\n  width: 100% !important;\r\n}\r\n\r\n.filter-button-wrapper {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  white-space: nowrap;\r\n  padding-left: 10px;\r\n  margin-left: auto;\r\n}\r\n\r\n.filter-button-wrapper .el-form-item {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-row {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.filter-row .el-form-item {\r\n  margin-bottom: 10px;\r\n  width: 100%;\r\n}\r\n\r\n.filter-row .el-select,\r\n.filter-row .el-input,\r\n.filter-row .el-date-editor {\r\n  width: 100% !important;\r\n}\r\n\r\n.filter-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 10px 0;\r\n  border-top: 1px solid #ebeef5;\r\n}\r\n\r\n.filter-buttons .el-button {\r\n  margin-left: 10px;\r\n}\r\n\r\n/* 响应式布局调整 */\r\n@media screen and (max-width: 1400px) {\r\n  .filter-row .el-col-lg-4 {\r\n    width: 33.33%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 992px) {\r\n  .filter-row .el-col-md-8 {\r\n    width: 50%;\r\n  }\r\n}\r\n\r\n@media screen and (max-width: 768px) {\r\n  .filter-row .el-col-sm-12 {\r\n    width: 100%;\r\n  }\r\n}\r\n/* 操作按钮容器 */\r\n.operation-buttons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  width: 90px;\r\n  padding: 4px 8px;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.operation-btn {\r\n  width: 74px !important;\r\n  height: 26px !important;\r\n  margin: 0 !important;\r\n  padding: 0 2px !important;\r\n  border-radius: 4px;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n  white-space: nowrap;\r\n  text-align: center;\r\n  line-height: 26px;\r\n}\r\n\r\n/* 按钮悬停效果 */\r\n.operation-btn:hover {\r\n  background-color: #f5f7fa;\r\n  color: #409eff;\r\n}\r\n</style>\r\n<style>\r\n.custom-popover {\r\n  width: 116px !important;\r\n  min-width: 116px !important;\r\n  max-width: 116px !important;\r\n  box-sizing: border-box !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAq5BA,IAAAA,gBAAA,GAAAC,OAAA;AAeA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,QAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,gBAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,sBAAA,GAAAH,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,UAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,qBAAA,EAAAA;EACA;EACAC,KAAA;IACAC,KAAA,GAAAC,MAAA,EAAAC,MAAA,EAAAC,KAAA;IACA;IACAC,MAAA;MACAC,IAAA,EAAAJ,MAAA;MACA;MACAK,OAAA;IACA;IACA;IACAC,IAAA;MACAF,IAAA,EAAAH;IACA;EACA;EACAM,IAAA;EACAD,IAAA,WAAAA,KAAA;IACA;MACA;MACAE,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,mBAAA;MACA;MACAC,OAAA;MAAA;MACAC,UAAA;MAAA;MACAC,YAAA;MAAA;MACAC,UAAA;MAAA;MACAC,WAAA;MAAA;MACAC,UAAA;MAAA;MACAC,UAAA;MAAA;MACAC,YAAA;QACAC,UAAA;QACAC,OAAA;MACA;MACAC,OAAA;MAAA;MACAC,OAAA;MAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,MAAA;QACAL,OAAA;QACAM,SAAA;QACAC,MAAA;QACAC,cAAA;QACAC,YAAA;QACAC,QAAA;QACAC,cAAA;QACAC,SAAA;QACAC,OAAA;QACAC,WAAA;QACAC,SAAA;QACAC,SAAA;MACA;MAEAC,QAAA,GACA;QAAA7C,KAAA;QAAA8C,KAAA;MAAA,GACA;QAAA9C,KAAA;QAAA8C,KAAA;MAAA,GACA;QAAA9C,KAAA;QAAA8C,KAAA;MAAA,GACA;QAAA9C,KAAA;QAAA8C,KAAA;MAAA,GACA;QAAA9C,KAAA;QAAA8C,KAAA;MAAA,GACA;QAAA9C,KAAA;QAAA8C,KAAA;MAAA,EACA;MAEAC,eAAA,GACA;QAAAD,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,EACA;MACAgD,YAAA,GACA;QAAAF,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,EACA;MACAiD,YAAA,GACA;QAAAH,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,EACA;MACAkD,WAAA;MAAA;MACA;MACAC,KAAA;MACAC,cAAA;MACAC,aAAA;MACAC,QAAA;MACAC,YAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,QAAAtD,MAAA;MAAA;MACAuD,OAAA;QACAC,aAAA,kBAAAC,cAAA;MACA;MACAC,YAAA;MAAA;MACAC,YAAA;MACAC,QAAA;MACAC,UAAA;MACAC,KAAA;MACAC,OAAA;MAAA;MACAC,SAAA;MAAA;MACAC,OAAA;QACAC,MAAA;QACAxC,OAAA;QACAC,QAAA;MACA;MACAwC,QAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAH,MAAA;MACAI,KAAA;MAAA;MACAC,MAAA;MACAC,MAAA;MACAC,mBAAA;MACAC,mBAAA;MACAC,yBAAA;MACAC,sBAAA;MACAC,qBAAA;MACAC,qBAAA;MACAC,2BAAA;MACAC,wBAAA;MACAC,SAAA;MACAC,kBAAA;MACAC,uBAAA;MACAC,iBAAA;MACAC,YAAA;MACAC,uBAAA;MACAC,0BAAA;MACAC,cAAA;MACAC,kBAAA;MACAC,SAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,SAAA;MAAA;MACAC,YAAA;MAAA;MACAC,aAAA,GACA;QAAAtD,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA,EACA;MACAqG,aAAA,GACA;QAAAvD,KAAA;QAAA9C,KAAA;MAAA,GACA;QAAA8C,KAAA;QAAA9C,KAAA;MAAA;IAEA;EACA;EACAsG,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,iCAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxD,WAAA,GAAA2D,QAAA,CAAAC,IAAA;MACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAA1G,IAAA;QACA2G,EAAA,EAAAF,GAAA,CAAA1C,MAAA;QACA6C,OAAA;MACA;MACA,IAAAC,iCAAA,EAAA7G,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAQ,IAAA;UACAJ,MAAA,CAAAK,MAAA,CAAAC,UAAA;QACA;UACAN,MAAA,CAAAK,MAAA,CAAAE,QAAA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAnH,IAAA;QACAmB,OAAA,OAAA2D,SAAA,CAAA3D,OAAA;QACAwF,EAAA,OAAA7B,SAAA,CAAA6B,EAAA;QACA5C,MAAA,OAAAe,SAAA,CAAAf,MAAA;QACAqD,UAAA,OAAAtC,SAAA,CAAAsC,UAAA;QACAzF,SAAA,OAAAmD,SAAA,CAAAnD;MACA;MACA,IAAA0F,gCAAA,EAAArH,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACAa,MAAA,CAAApC,kBAAA,GAAAuB,QAAA,CAAAtG,IAAA,CAAAsH,SAAA;QACAH,MAAA,CAAArC,SAAA,CAAAyC,eAAA,GAAAjB,QAAA,CAAAtG,IAAA,CAAAuH,eAAA;QACAJ,MAAA,CAAAlC,iBAAA,GAAAqB,QAAA,CAAAtG,IAAA,CAAAwH,QAAA;QACAL,MAAA,CAAAjC,YAAA,GAAAoB,QAAA,CAAAtG,IAAA,CAAAyH,WAAA;QACAN,MAAA,CAAAhC,uBAAA,GAAAmB,QAAA,CAAAtG,IAAA,CAAA0H,WAAA;QACAP,MAAA,CAAA/B,0BAAA,GAAAkB,QAAA,CAAAtG,IAAA,CAAA2H,iBAAA;QACAR,MAAA,CAAA9B,cAAA,GAAAuC,MAAA,CACAT,MAAA,CAAAjC,YAAA,GAAAiC,MAAA,CAAAhC,uBAAA,GAAAgC,MAAA,CAAA/B,0BAAA,GAAA+B,MAAA,CAAA7B,kBACA,EAAAuC,OAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAArD,qBAAA,UAAAhB,UAAA,CAAAsE,cAAA;QACA,KAAAjB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,SAAAtC,qBAAA,UAAAjB,UAAA,CAAAuE,cAAA;QACA,KAAAlB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,SAAArC,2BAAA,UAAAlB,UAAA,CAAAwE,cAAA;QACA,KAAAnB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,SAAAvD,UAAA,CAAAyE,SAAA,UAAAzE,UAAA,CAAA0E,cAAA;QACA,KAAArB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,SAAApC,wBAAA,UAAAnB,UAAA,CAAA2E,cAAA;QACA,KAAAtB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,IAAAjH,IAAA;QACAyB,YAAA,OAAAiC,UAAA,CAAAjC,YAAA;QACA6G,OAAA,OAAA5E,UAAA,CAAA4E,OAAA;QACAC,IAAA,OAAA7E,UAAA,CAAA6E,IAAA;QACAnB,UAAA,OAAA1D,UAAA,CAAA0D,UAAA;QACAK,WAAA,OAAA/D,UAAA,CAAA+D,WAAA;QACAO,cAAA,OAAAtE,UAAA,CAAAsE,cAAA;QACAN,WAAA,OAAAhE,UAAA,CAAAgE,WAAA;QACAO,cAAA,OAAAvE,UAAA,CAAAuE,cAAA;QACAN,iBAAA,OAAAjE,UAAA,CAAAiE,iBAAA;QACAO,cAAA,OAAAxE,UAAA,CAAAwE,cAAA;QACAC,SAAA,OAAAzE,UAAA,CAAAyE,SAAA;QACAC,cAAA,OAAA1E,UAAA,CAAA0E,cAAA;QACAI,cAAA,OAAA9E,UAAA,CAAA8E,cAAA;QACAH,cAAA,OAAA3E,UAAA,CAAA2E,cAAA;QACAI,UAAA,OAAAvE,cAAA;QACAyC,EAAA,OAAAxC,KAAA;QACAuE,MAAA;QACAC,aAAA;MACA;MACA,IAAAC,kCAAA,EAAA5I,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAQ,IAAA;UACAiB,MAAA,CAAAhB,MAAA,CAAAC,UAAA;UACAe,MAAA,CAAAlH,UAAA;UACAkH,MAAA,CAAArE,UAAA;UACAqE,MAAA,CAAArD,qBAAA;UACAqD,MAAA,CAAApD,qBAAA;UACAoD,MAAA,CAAAnD,2BAAA;UACAmD,MAAA,CAAAlD,wBAAA;QACA;MACA;IACA;IACAgE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAAxE,mBAAA,UAAAb,QAAA,CAAAuE,cAAA;QACA,KAAAjB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,SAAA1C,mBAAA,UAAAd,QAAA,CAAAwE,cAAA;QACA,KAAAlB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,SAAAzC,yBAAA,UAAAf,QAAA,CAAAyE,cAAA;QACA,KAAAnB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,SAAAxD,QAAA,CAAA0E,SAAA,UAAA1E,QAAA,CAAA2E,cAAA;QACA,KAAArB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,SAAAxC,sBAAA,UAAAhB,QAAA,CAAA4E,cAAA;QACA,KAAAtB,MAAA,CAAAE,QAAA;QACA;MACA;MACA,IAAAjH,IAAA;QACAyB,YAAA,OAAAgC,QAAA,CAAAhC,YAAA;QACA6G,OAAA,OAAA7E,QAAA,CAAA6E,OAAA;QACAC,IAAA,OAAA9E,QAAA,CAAA8E,IAAA;QACAnB,UAAA,OAAA3D,QAAA,CAAA2D,UAAA;QACAK,WAAA,OAAAhE,QAAA,CAAAgE,WAAA;QACAO,cAAA,OAAAvE,QAAA,CAAAuE,cAAA;QACAN,WAAA,OAAAjE,QAAA,CAAAiE,WAAA;QACAO,cAAA,OAAAxE,QAAA,CAAAwE,cAAA;QACAN,iBAAA,OAAAlE,QAAA,CAAAkE,iBAAA;QACAO,cAAA,OAAAzE,QAAA,CAAAyE,cAAA;QACAC,SAAA,OAAA1E,QAAA,CAAA0E,SAAA;QACAC,cAAA,OAAA3E,QAAA,CAAA2E,cAAA;QACAI,cAAA,OAAA/E,QAAA,CAAA+E,cAAA;QACAH,cAAA,OAAA5E,QAAA,CAAA4E,cAAA;QACAI,UAAA,OAAAxE,YAAA;QACA0C,EAAA,OAAAxC,KAAA;QACAuE,MAAA;QACAC,aAAA;MACA;MACA,IAAAC,kCAAA,EAAA5I,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAQ,IAAA;UACAgC,MAAA,CAAA/B,MAAA,CAAAC,UAAA;UACA8B,MAAA,CAAAlI,YAAA;UACAkI,MAAA,CAAArF,QAAA;UACAqF,MAAA,CAAAxE,mBAAA;UACAwE,MAAA,CAAAvE,mBAAA;UACAuE,MAAA,CAAAtE,yBAAA;UACAsE,MAAA,CAAArE,sBAAA;QACA;MACA;IACA;IACAsE,YAAA,WAAAA,aAAAC,GAAA,EAAAvJ,KAAA;MAAA,IAAAwJ,GAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAG,GAAA,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAAI,GAAA,GAAA1B,MAAA,CAAAnI,KAAA;MACA,IAAA8J,KAAA,CAAAD,GAAA,GAAAA,GAAA,GAAAL,GAAA;MACA,IAAAK,GAAA,GAAAD,GAAA,EAAAC,GAAA,GAAAD,GAAA;MACA,IAAAC,GAAA,GAAAL,GAAA,EAAAK,GAAA,GAAAL,GAAA;MACA,KAAAnE,SAAA,CAAAkE,GAAA,IAAApB,MAAA,CAAA0B,GAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAAzI,UAAA;IACA;IACA;IACA0I,QAAA,WAAAA,SAAApI,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAD,OAAA;IACA;IACA;IACAsI,WAAA,WAAAA,YAAAzI,YAAA;MACA,KAAAA,YAAA,GAAAA,YAAA;MACA,KAAAD,UAAA;IACA;IACA2I,OAAA,WAAAA,QAAAlD,GAAA;MACA,IAAAzG,IAAA;QACA2B,SAAA,EAAA8E,GAAA,CAAA9E,SAAA;QACAR,OAAA,EAAAsF,GAAA,CAAAtF;MACA;MACA,IAAAsF,GAAA,CAAA9E,SAAA;QACA,KAAAiI,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA,EAAA/J;QACA;MACA,WAAAyG,GAAA,CAAA9E,SAAA;QACA,KAAAiI,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA,EAAA/J;QACA;MACA,WAAAyG,GAAA,CAAA9E,SAAA;QACA,KAAAiI,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA,EAAA/J;QACA;MACA,WAAAyG,GAAA,CAAA9E,SAAA;QACA,KAAAiI,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA,EAAA/J;QACA;MACA,WAAAyG,GAAA,CAAA9E,SAAA;QACA,KAAAiI,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA,EAAA/J;QACA;MACA;QACA,KAAA4J,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA,EAAA/J;QACA;MACA;IACA;IACAgK,aAAA,WAAAA,cAAArD,EAAA;MAAA,IAAAsD,MAAA;MACA,IAAAjK,IAAA;QACA+D,MAAA,EAAA4C,EAAA;QACAuD,UAAA;MACA;MACA,IAAAC,oCAAA,EAAAnK,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACA2D,MAAA,CAAA1G,YAAA,GAAA+C,QAAA,CAAAC,IAAA;QACA0D,MAAA,CAAAzG,YAAA;MACA;IACA;IAEA4G,aAAA,WAAAA,cAAA;MACA,KAAAtJ,WAAA;IACA;IACAuJ,QAAA,WAAAA,SAAA5D,GAAA;MAAA,IAAA6D,MAAA;MACA;MACA,IAAAtK,IAAA;QACA+D,MAAA,EAAA0C,GAAA,CAAA1C;MACA;MACA,IAAAwG,wCAAA,EAAAvK,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAtG,IAAA;UACAsK,MAAA,CAAAxF,SAAA,GAAAwB,QAAA,CAAAtG,IAAA;UACAsK,MAAA,CAAAxF,SAAA,CAAAsC,UAAA,GAAAX,GAAA,CAAA+D,WAAA;UACAF,MAAA,CAAAxF,SAAA,CAAA4C,WAAA,GAAApB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA/C,WAAA;UACA4C,MAAA,CAAAvF,kBAAA,GAAAuB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAnD,SAAA;UACAgD,MAAA,CAAAtF,uBAAA,GAAAyB,GAAA,CAAAiE,cAAA;UACAJ,MAAA,CAAArF,iBAAA,GAAAqB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAjD,QAAA;UACA8C,MAAA,CAAApF,YAAA,GAAAoB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAhD,WAAA;UACA6C,MAAA,CAAAnF,uBAAA,GAAAmB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA/C,WAAA;UACA4C,MAAA,CAAAlF,0BAAA,GAAAkB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA9C,iBAAA;UACA2C,MAAA,CAAAhF,kBAAA,GAAAgB,QAAA,CAAAtG,IAAA,CAAAmI,SAAA,GAAA7B,QAAA,CAAAtG,IAAA,CAAAmI,SAAA;UACAwC,OAAA,CAAAC,GAAA,CAAAN,MAAA,CAAApF,YAAA,EAAAoF,MAAA,CAAAnF,uBAAA,EAAAmF,MAAA,CAAAlF,0BAAA,EAAAkF,MAAA,CAAAhF,kBAAA;UACAgF,MAAA,CAAAjF,cAAA,GAAAuC,MAAA,CACA0C,MAAA,CAAApF,YAAA,GAAAoF,MAAA,CAAAnF,uBAAA,GAAAmF,MAAA,CAAAlF,0BAAA,GAAAkF,MAAA,CAAAhF,kBACA,EAAAuC,OAAA;UACAyC,MAAA,CAAAxF,SAAA,CAAA+F,KAAA,GAAAvE,QAAA,CAAAtG,IAAA,CAAA6K,KAAA,GAAAvE,QAAA,CAAAtG,IAAA,CAAA6K,KAAA,CAAAC,KAAA;UAEAR,MAAA,CAAAxJ,WAAA;QACA;UACAwJ,MAAA,CAAAS,QAAA,CAAAtE,GAAA;UACA6D,MAAA,CAAAxF,SAAA,CAAA+F,KAAA;QACA;MACA;IACA;IACAE,QAAA,WAAAA,SAAAtE,GAAA;MAAA,IAAAuE,MAAA;MACAL,OAAA,CAAAC,GAAA,CAAAnE,GAAA;MACA,IAAAzG,IAAA;QACA2G,EAAA,EAAAF,GAAA,CAAAE,EAAA;QACAxF,OAAA,EAAAsF,GAAA,CAAAtF,OAAA;QACA4C,MAAA,EAAA0C,GAAA,CAAA1C,MAAA;QACA7C,UAAA,EAAAuF,GAAA,CAAAvF,UAAA;QACAO,YAAA,EAAAgF,GAAA,CAAAhF,YAAA;QACAwJ,QAAA,EAAAxE,GAAA,CAAAyE,QAAA;QACA5C,OAAA,EAAA7B,GAAA,CAAA7E,MAAA;QACAD,SAAA,EAAA8E,GAAA,CAAA9E,SAAA;QACA4G,IAAA,EAAA9B,GAAA,CAAA6B,OAAA;QACAlB,UAAA,EAAAX,GAAA,CAAA+D,WAAA;QACA7B,aAAA;MACA;MACA,IAAAwC,gCAAA,EAAAnL,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACA0E,MAAA,CAAAlG,SAAA,GAAAwB,QAAA,CAAAtG,IAAA;QACAgL,MAAA,CAAAlK,WAAA;MACA;IACA;IACAsK,YAAA,WAAAA,aAAA;MACA,KAAA1H,UAAA;MACA,KAAAU,MAAA;MACA,KAAAM,qBAAA;MACA,KAAAC,qBAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,wBAAA;MACA,KAAAX,cAAA;MACA,KAAArD,UAAA;IACA;IACAwK,YAAA,WAAAA,aAAA5L,KAAA;MACA,KAAA6F,kBAAA,GAAAsC,MAAA,CAAAnI,KAAA;MACA,KAAA4F,cAAA,GAAAuC,MAAA,CACA,KAAA1C,YAAA,QAAAC,uBAAA,QAAAC,0BAAA,QAAAE,kBACA,EAAAuC,OAAA;IACA;IACAyD,YAAA,WAAAA,aAAA7L,KAAA;MACA,KAAAiE,UAAA,CAAAyE,SAAA,GAAAP,MAAA,CAAAnI,KAAA;MACA,KAAAyE,cAAA,GAAA0D,MAAA,CACA,KAAAlE,UAAA,CAAA+D,WAAA,GACA,KAAA/D,UAAA,CAAAgE,WAAA,GACA,KAAAhE,UAAA,CAAAiE,iBAAA,GACA,KAAAjE,UAAA,CAAAyE,SAAA,GACA,KAAAzE,UAAA,CAAA8E,cACA,EAAAX,OAAA;IACA;IACA0D,YAAA,WAAAA,aAAA9L,KAAA;MACA,KAAAgE,QAAA,CAAA0E,SAAA,GAAAP,MAAA,CAAAnI,KAAA;MACA,KAAAwE,YAAA,GAAA2D,MAAA,CACA,KAAAnE,QAAA,CAAAgE,WAAA,GACA,KAAAhE,QAAA,CAAAiE,WAAA,GACA,KAAAjE,QAAA,CAAAkE,iBAAA,GACA,KAAAlE,QAAA,CAAA0E,SAAA,GACA,KAAA1E,QAAA,CAAA+E,cACA,EAAAX,OAAA;IACA;IACA2D,YAAA,WAAAA,aAAA/E,GAAA;MAAA,IAAAgF,MAAA;MACA,IAAAzL,IAAA;QACA+D,MAAA,EAAA0C,GAAA,CAAA1C,MAAA;QACA2E,MAAA;QACAlH,QAAA;QACAD,OAAA;MACA;MACA,KAAAwC,MAAA,GAAA0C,GAAA,CAAA1C,MAAA;MACA,IAAA2H,oCAAA,EAAA1L,IAAA,EACAqG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAtG,IAAA;UACAyL,MAAA,CAAA/H,UAAA,GAAA4C,QAAA,CAAAtG,IAAA;UACA,KAAAsG,QAAA,CAAAtG,IAAA,CAAAyB,YAAA;YACAgK,MAAA,CAAA/H,UAAA,CAAAjC,YAAA,GAAAgF,GAAA,CAAAhF,YAAA;UACA;UACA,KAAA6E,QAAA,CAAAtG,IAAA,CAAAsI,OAAA;YACAmD,MAAA,CAAA/H,UAAA,CAAA4E,OAAA,GAAA7B,GAAA,CAAA7E,MAAA;UACA;UACA,KAAA0E,QAAA,CAAAtG,IAAA,CAAAuI,IAAA;YACAkD,MAAA,CAAA/H,UAAA,CAAA6E,IAAA,GAAA9B,GAAA,CAAA6B,OAAA;UACA;UACA,KAAAhC,QAAA,CAAAtG,IAAA,CAAAoH,UAAA;YACAqE,MAAA,CAAA/H,UAAA,CAAA0D,UAAA,GAAAX,GAAA,CAAA+D,WAAA;UACA;UACAiB,MAAA,CAAA/H,UAAA,CAAA+D,WAAA,GAAAnB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAhD,WAAA;UACAgE,MAAA,CAAA/H,UAAA,CAAAgE,WAAA,GAAApB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA/C,WAAA;UACA+D,MAAA,CAAA/H,UAAA,CAAA8E,cAAA,GAAAlC,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAjC,cAAA;UACAiD,MAAA,CAAA/H,UAAA,CAAAiE,iBAAA,GAAArB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA9C,iBAAA;UACA8D,MAAA,CAAA/G,qBAAA,GAAA4B,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAhD,WAAA;UACAgE,MAAA,CAAA9G,qBAAA,GAAA2B,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA/C,WAAA;UACA+D,MAAA,CAAA5G,wBAAA,GAAAyB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAjC,cAAA;UACAiD,MAAA,CAAA7G,2BAAA,GAAA0B,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA9C,iBAAA;UACA8D,MAAA,CAAAvH,cAAA,GAAA0D,MAAA,CACA6D,MAAA,CAAA/H,UAAA,CAAA+D,WAAA,GAAAgE,MAAA,CAAA/H,UAAA,CAAAgE,WAAA,GAAA+D,MAAA,CAAA/H,UAAA,CAAAiE,iBAAA,GAAA8D,MAAA,CAAA/H,UAAA,CAAA8E,cACA,EAAAX,OAAA;UACA4D,MAAA,CAAAtH,KAAA,GAAAmC,QAAA,CAAAtG,IAAA,CAAA2G,EAAA;UACA8E,MAAA,CAAArH,MAAA,GAAAkC,QAAA,CAAAtG,IAAA;QACA;UACAyL,MAAA,CAAAE,SAAA,CAAAlF,GAAA;UACA;UACA;UACA;UACA;UACA;QACA;MACA,GACAmF,KAAA,WAAAC,KAAA;MACA,KAAAhL,UAAA;IACA;IACAiL,cAAA,WAAAA,eAAA;MACA,KAAArI,QAAA;MACA,KAAAY,MAAA;MACA,KAAAC,mBAAA;MACA,KAAAC,mBAAA;MACA,KAAAC,yBAAA;MACA,KAAAC,sBAAA;MACA,KAAAR,YAAA;MACA,KAAArD,YAAA;IACA;IACAmL,YAAA,WAAAA,aAAAC,CAAA;MAAA,IAAAC,MAAA;MACA,IAAAjM,IAAA;MACA,IAAAgM,CAAA;QACAhM,IAAA,QAAAqE,MAAA;MACA;QACArE,IAAA,QAAAoE,MAAA;MACA;MACA,IAAA8H,mCAAA,EAAAlM,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACA,IAAA0F,CAAA;UACA;UACAC,MAAA,CAAAxI,QAAA,CAAAgE,WAAA,GAAAnB,QAAA,CAAAtG,IAAA,CAAAyH,WAAA;UACAwE,MAAA,CAAAxI,QAAA,CAAAiE,WAAA,GAAApB,QAAA,CAAAtG,IAAA,CAAA0H,WAAA;UACAuE,MAAA,CAAAxI,QAAA,CAAAkE,iBAAA,GAAArB,QAAA,CAAAtG,IAAA,CAAA2H,iBAAA;UACAsE,MAAA,CAAAxI,QAAA,CAAA+E,cAAA,GAAAlC,QAAA,CAAAtG,IAAA,CAAAwI,cAAA;UACAyD,MAAA,CAAA3H,mBAAA,GAAAgC,QAAA,CAAAtG,IAAA,CAAAyH,WAAA;UACAwE,MAAA,CAAA1H,mBAAA,GAAA+B,QAAA,CAAAtG,IAAA,CAAA0H,WAAA;UACAuE,MAAA,CAAAzH,yBAAA,GAAA8B,QAAA,CAAAtG,IAAA,CAAA2H,iBAAA;UACAsE,MAAA,CAAAxH,sBAAA,GAAA6B,QAAA,CAAAtG,IAAA,CAAAwI,cAAA;UACAyD,MAAA,CAAAhI,YAAA,GAAA2D,MAAA,CACAqE,MAAA,CAAAxI,QAAA,CAAAgE,WAAA,GACAwE,MAAA,CAAAxI,QAAA,CAAAiE,WAAA,GACAuE,MAAA,CAAAxI,QAAA,CAAAkE,iBAAA,GACAsE,MAAA,CAAAxI,QAAA,CAAA0E,SAAA,GACA8D,MAAA,CAAAxI,QAAA,CAAA+E,cACA,EAAAX,OAAA;QACA;UACA;UACAoE,MAAA,CAAAvI,UAAA,CAAA+D,WAAA,GAAAnB,QAAA,CAAAtG,IAAA,CAAAyH,WAAA;UACAwE,MAAA,CAAAvI,UAAA,CAAAgE,WAAA,GAAApB,QAAA,CAAAtG,IAAA,CAAA0H,WAAA;UACAuE,MAAA,CAAAvI,UAAA,CAAAiE,iBAAA,GAAArB,QAAA,CAAAtG,IAAA,CAAA2H,iBAAA;UACAsE,MAAA,CAAAvI,UAAA,CAAA8E,cAAA,GAAAlC,QAAA,CAAAtG,IAAA,CAAAwI,cAAA;UACAyD,MAAA,CAAAvH,qBAAA,GAAA4B,QAAA,CAAAtG,IAAA,CAAAyH,WAAA;UACAwE,MAAA,CAAAtH,qBAAA,GAAA2B,QAAA,CAAAtG,IAAA,CAAA0H,WAAA;UACAuE,MAAA,CAAArH,2BAAA,GAAA0B,QAAA,CAAAtG,IAAA,CAAA2H,iBAAA;UACAsE,MAAA,CAAApH,wBAAA,GAAAyB,QAAA,CAAAtG,IAAA,CAAAwI,cAAA;UACAyD,MAAA,CAAA/H,cAAA,GAAA0D,MAAA,CACAqE,MAAA,CAAAvI,UAAA,CAAA+D,WAAA,GACAwE,MAAA,CAAAvI,UAAA,CAAAgE,WAAA,GACAuE,MAAA,CAAAvI,UAAA,CAAAiE,iBAAA,GACAsE,MAAA,CAAAvI,UAAA,CAAAyE,SAAA,GACA8D,MAAA,CAAAvI,UAAA,CAAA8E,cACA,EAAAX,OAAA;QACA;MACA;IACA;IACAsE,cAAA,WAAAA,eAAA1F,GAAA;MAAA,IAAA2F,MAAA;MACA,IAAApM,IAAA;QACA+D,MAAA,EAAA0C,GAAA,CAAA1C,MAAA;QACA2E,MAAA;QACAlH,QAAA;QACAD,OAAA;MACA;MACA,KAAAwC,MAAA,GAAA0C,GAAA,CAAA1C,MAAA;MACA,IAAA2H,oCAAA,EAAA1L,IAAA,EACAqG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAtG,IAAA;UACAoM,MAAA,CAAA3I,QAAA,GAAA6C,QAAA,CAAAtG,IAAA;UACA,KAAAsG,QAAA,CAAAtG,IAAA,CAAAyB,YAAA;YACA2K,MAAA,CAAA3I,QAAA,CAAAhC,YAAA,GAAAgF,GAAA,CAAAhF,YAAA;UACA;UACA,KAAA6E,QAAA,CAAAtG,IAAA,CAAAsI,OAAA;YACA8D,MAAA,CAAA3I,QAAA,CAAA6E,OAAA,GAAA7B,GAAA,CAAA7E,MAAA;UACA;UACA,KAAA0E,QAAA,CAAAtG,IAAA,CAAAuI,IAAA;YACA6D,MAAA,CAAA3I,QAAA,CAAA8E,IAAA,GAAA9B,GAAA,CAAA6B,OAAA;UACA;UACA,KAAAhC,QAAA,CAAAtG,IAAA,CAAAoH,UAAA;YACAgF,MAAA,CAAA3I,QAAA,CAAA2D,UAAA,GAAAX,GAAA,CAAA+D,WAAA;UACA;UACA4B,MAAA,CAAA3I,QAAA,CAAAgE,WAAA,GAAAnB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAhD,WAAA;UACA2E,MAAA,CAAA3I,QAAA,CAAAiE,WAAA,GAAApB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA/C,WAAA;UACA0E,MAAA,CAAA3I,QAAA,CAAAkE,iBAAA,GAAArB,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA9C,iBAAA;UACAyE,MAAA,CAAA3I,QAAA,CAAA+E,cAAA,GAAAlC,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAjC,cAAA;UACA4D,MAAA,CAAA9H,mBAAA,GAAAgC,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAhD,WAAA;UACA2E,MAAA,CAAA7H,mBAAA,GAAA+B,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA/C,WAAA;UACA0E,MAAA,CAAA5H,yBAAA,GAAA8B,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAA9C,iBAAA;UACAyE,MAAA,CAAA3H,sBAAA,GAAA6B,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,GAAAnE,QAAA,CAAAtG,IAAA,CAAAyK,YAAA,CAAAjC,cAAA;UACA4D,MAAA,CAAAnI,YAAA,GAAA2D,MAAA,CACAwE,MAAA,CAAA3I,QAAA,CAAAgE,WAAA,GAAA2E,MAAA,CAAA3I,QAAA,CAAAiE,WAAA,GAAA0E,MAAA,CAAA3I,QAAA,CAAAkE,iBAAA,GAAAyE,MAAA,CAAA3I,QAAA,CAAA+E,cACA,EAAAX,OAAA;UACAuE,MAAA,CAAAjI,KAAA,GAAAmC,QAAA,CAAAtG,IAAA,CAAA2G,EAAA;UACAyF,MAAA,CAAA/H,MAAA,GAAAiC,QAAA,CAAAtG,IAAA;QACA;UACAoM,MAAA,CAAAT,SAAA,CAAAlF,GAAA;UACA;UACA;UACA;UACA;UACA;QACA;QACA2F,MAAA,CAAAxL,YAAA;MACA,GACAgL,KAAA,WAAAC,KAAA;IACA;IACAF,SAAA,WAAAA,UAAAlF,GAAA,EAAAuF,CAAA;MAAA,IAAAK,OAAA;MACA,IAAArM,IAAA;QACAmB,OAAA,EAAAsF,GAAA,CAAAtF,OAAA;QACA4C,MAAA,EAAA0C,GAAA,CAAA1C,MAAA;QACAtC,YAAA,EAAAgF,GAAA,CAAAhF,YAAA;QACA6G,OAAA,EAAA7B,GAAA,CAAA7E,MAAA;QACAwF,UAAA,EAAAX,GAAA,CAAA+D,WAAA;QACAjC,IAAA,EAAA9B,GAAA,CAAA6B,OAAA;QACA3G,SAAA,EAAA8E,GAAA,CAAA9E,SAAA;QACA+G,MAAA,EAAAsD,CAAA;QACAf,QAAA,EAAAxE,GAAA,CAAAyE,QAAA;QACAoB,WAAA,EAAA7F,GAAA,CAAA8F,YAAA;QACA5D,aAAA;MACA;MACA,IAAA6D,mCAAA,EAAAxM,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACA+F,OAAA,CAAAlI,KAAA,GAAAmC,QAAA,CAAAK,EAAA;QACA,IAAAqF,CAAA;UACAK,OAAA,CAAA5I,QAAA,GAAA6C,QAAA,CAAAtG,IAAA;UACAqM,OAAA,CAAAhI,MAAA,GAAAiC,QAAA,CAAAtG,IAAA;QACA;UACAqM,OAAA,CAAA3I,UAAA,GAAA4C,QAAA,CAAAtG,IAAA;UACAqM,OAAA,CAAAjI,MAAA,GAAAkC,QAAA,CAAAtG,IAAA;QACA;MACA;IACA;IACAyM,SAAA,WAAAA,UAAA;MACA,KAAA/L,OAAA;IACA;IACAgM,OAAA,WAAAA,QAAAjG,GAAA;MACA,KAAAd,SAAA,GAAAc,GAAA,CAAA1C,MAAA;MACA,KAAA4I,KAAA,CAAAC,eAAA,CAAAC,aAAA;IACA;IACAC,oBAAA,WAAAA,qBAAArG,GAAA;MAAA,IAAAsG,OAAA;MACA,KAAAnH,YAAA,GAAAa,GAAA,CAAA1C,MAAA;MACA,KAAAiJ,SAAA;QACAD,OAAA,CAAAJ,KAAA,CAAAM,qBAAA,CAAAC,UAAA;MACA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA,IAAAjD,oCAAA,OAAArG,OAAA,EACAuC,IAAA,WAAAC,QAAA;QACA8G,OAAA,CAAAxJ,OAAA,GAAA0C,QAAA,CAAAC,IAAA;QACA6G,OAAA,CAAApJ,QAAA,GAAAsC,QAAA,CAAA9F,KAAA;QACA4M,OAAA,CAAA1M,OAAA;MACA,GACAkL,KAAA,WAAAC,KAAA;IACA;IAEAwB,YAAA,WAAAA,aAAA5N,KAAA;MACA,KAAA6B,WAAA,CAAAgM,KAAA,GAAA7N,KAAA;IACA;IACA,eACAuG,OAAA,WAAAA,QAAA;MAAA,IAAAuH,OAAA;MACA5C,OAAA,CAAAC,GAAA,MAAA4C,MAAA,CAAAC,KAAA,CAAAC,IAAA;MACA,KAAAxN,OAAA;MACA,IAAAyN,oCAAA,OAAArM,WAAA,EAAA+E,IAAA,WAAAC,QAAA;QACAiH,OAAA,CAAA9M,mBAAA,GAAA6F,QAAA,CAAAC,IAAA;QACAgH,OAAA,CAAA/M,KAAA,GAAA8F,QAAA,CAAA9F,KAAA;QACA+M,OAAA,CAAArN,OAAA;MACA;IACA;IAEA;IACA0N,KAAA,WAAAA,MAAA;MACA,KAAAC,IAAA;QACApM,YAAA;QACAG,MAAA;QACA0G,OAAA;QACAoC,cAAA;QACAoD,cAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,SAAA1M,WAAA,CAAAU,cAAA;QACA,KAAAV,WAAA,CAAAW,SAAA,QAAAX,WAAA,CAAAU,cAAA;QACA,KAAAV,WAAA,CAAAY,OAAA,QAAAZ,WAAA,CAAAU,cAAA;MACA;MACA,KAAAV,WAAA,CAAAC,OAAA;MACA,KAAAyE,OAAA;IACA;IACA,aACAiI,UAAA,WAAAA,WAAA;MACA,KAAA3M,WAAA,CAAAG,YAAA;MACA,KAAAH,WAAA,CAAAI,MAAA;MACA,KAAAJ,WAAA,CAAAD,OAAA;MACA,KAAAC,WAAA,CAAAK,SAAA;MACA,KAAAL,WAAA,CAAAM,MAAA;MACA,KAAAN,WAAA,CAAAO,cAAA;MACA,KAAAP,WAAA,CAAAQ,YAAA;MACA,KAAAR,WAAA,CAAAS,QAAA;MACA,KAAAT,WAAA,CAAAU,cAAA;MACA,KAAAV,WAAA,CAAAW,SAAA;MACA,KAAAX,WAAA,CAAAY,OAAA;MACA,KAAAZ,WAAA,CAAAa,WAAA;MACA,KAAAb,WAAA,CAAAc,SAAA;MACA,KAAAd,WAAA,CAAAe,SAAA;MACA,KAAA2L,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAhO,GAAA,GAAAgO,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA1H,EAAA;MAAA;MACA,KAAAvG,MAAA,GAAA+N,SAAA,CAAAhF,MAAA;MACA,KAAA9I,QAAA,IAAA8N,SAAA,CAAAhF,MAAA;IACA;IACA,aACAmF,SAAA,WAAAA,UAAA;MACA,KAAAV,KAAA;MACA,KAAAW,IAAA;MACA,KAAAC,KAAA;IACA;IAEAC,SAAA,WAAAA,UAAAC,aAAA,EAAAC,QAAA,EAAArF,GAAA;MACA;MACA,KAAAxE,SAAA,CAAA6J,QAAA,KAAArF,GAAA,QAAApE,YAAA,QAAA2C,OAAA;IACA;IACA,WACA+G,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,IAAA;MACA;MACA,IAAAtO,KAAA,GAAAsO,IAAA,CAAAC,MAAA,WAAAC,GAAA,EAAAhG,GAAA;QAAA,OAAAgG,GAAA,GAAApH,MAAA,CAAAiH,OAAA,CAAA/J,SAAA,CAAAkE,GAAA;MAAA;MACA,IAAAxI,KAAA;QACA,KAAAyO,QAAA,CAAApD,KAAA;QACA;MACA;;MAEA;MACA,IAAAqD,eAAA,IACA;QAAAC,KAAA;QAAAC,OAAA;QAAA7M,KAAA;MAAA,GACA;QAAA4M,KAAA;QAAAC,OAAA;QAAA7M,KAAA;MAAA,GACA;QAAA4M,KAAA;QAAAC,OAAA;QAAA7M,KAAA;MAAA,GACA;QAAA4M,KAAA;QAAAC,OAAA;QAAA7M,KAAA;MAAA,GACA;QACA4M,KAAA;QACAC,OAAA;QACA7M,KAAA;MACA,GACA;QACA4M,KAAA;QACAC,OAAA;QACA7M,KAAA;MACA,EACA;;MAEA;MACA,SAAA8M,EAAA,MAAAC,gBAAA,GAAAJ,eAAA,EAAAG,EAAA,GAAAC,gBAAA,CAAAnG,MAAA,EAAAkG,EAAA;QAAA,IAAAhB,IAAA,GAAAiB,gBAAA,CAAAD,EAAA;QACA,IAAAzH,MAAA,MAAA9C,SAAA,CAAAuJ,IAAA,CAAAc,KAAA,gBAAArK,SAAA,CAAAuJ,IAAA,CAAAe,OAAA;UACA,KAAAH,QAAA,CAAAM,OAAA,4BAAAC,MAAA,CAAAnB,IAAA,CAAA9L,KAAA;UACA;QACA;MACA;MACA,IAAAvC,IAAA;QACA2G,EAAA,OAAA7B,SAAA,CAAA6B,EAAA;QACA8I,aAAA,OAAA3K,SAAA,CAAA2K,aAAA;QACAC,QAAA,OAAA5K,SAAA,CAAA4K,QAAA;QACAC,UAAA,OAAA7K,SAAA,CAAA6K,UAAA;QACAC,YAAA,OAAA9K,SAAA,CAAA8K,YAAA;QACAC,OAAA,OAAA/K,SAAA,CAAA+K,OAAA;QACAC,SAAA,OAAAhL,SAAA,CAAAgL,SAAA;QACAC,aAAA,OAAAjL,SAAA,CAAAiL,aAAA;QACAC,QAAA,OAAAlL,SAAA,CAAAkL,QAAA;QACAC,UAAA,OAAAnL,SAAA,CAAAmL,UAAA;QACAC,aAAA,OAAApL,SAAA,CAAAoL,aAAA;QACAC,QAAA,OAAArL,SAAA,CAAAqL,QAAA;QACAC,UAAA,OAAAtL,SAAA,CAAAsL,UAAA;QACAC,cAAA,OAAAvL,SAAA,CAAAuL,cAAA;QACAC,SAAA,OAAAxL,SAAA,CAAAwL,SAAA;QACAC,WAAA,OAAAzL,SAAA,CAAAyL,WAAA;QACAC,cAAA,OAAA1L,SAAA,CAAA0L,cAAA;QACAC,SAAA,OAAA3L,SAAA,CAAA2L,SAAA;QACAC,WAAA,OAAA5L,SAAA,CAAA4L,WAAA;QACAvI,SAAA,OAAA7C,kBAAA;QACAmD,UAAA,OAAApD,cAAA;QACAwF,KAAA,OAAA/F,SAAA,CAAA+F,KAAA,CAAAuD,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAA/H,QAAA;QAAA,GAAAqK,IAAA;QACAhI,aAAA;QACAiI,aAAA;MACA;MACAjG,OAAA,CAAAC,GAAA,CAAA5K,IAAA;MACA,IAAA6Q,gCAAA,EAAA7Q,IAAA,EAAAqG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAQ,IAAA;UACA+H,OAAA,CAAA9H,MAAA,CAAAC,UAAA;UACA6H,OAAA,CAAA/N,WAAA;QACA;MACA;IACA;IACA,aACAgQ,YAAA,WAAAA,aAAArK,GAAA;MAAA,IAAAsK,OAAA;MACA,IAAA5Q,GAAA,GAAAsG,GAAA,CAAAE,EAAA,SAAAxG,GAAA;MACA,KAAA4G,MAAA,CACAiK,OAAA,oBAAA7Q,GAAA,aACAkG,IAAA;QACA,WAAA4K,mCAAA,EAAA9Q,GAAA;MACA,GACAkG,IAAA;QACA0K,OAAA,CAAA/K,OAAA;QACA+K,OAAA,CAAAhK,MAAA,CAAAC,UAAA;MACA,GACA4E,KAAA;IACA;IACA,aACAsF,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,8CAAAC,cAAA,CAAArR,OAAA,MAEA,KAAAuB,WAAA,sBAAAkO,MAAA,CAEA,IAAA6B,IAAA,GAAAC,OAAA,YACA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA;MACA5G,OAAA,CAAAC,GAAA,uBAAAnK,mBAAA;IACA;IAEA;IACA+Q,mBAAA,WAAAA,oBAAAC,GAAA,EAAAC,IAAA,EAAAC,QAAA,EAAAC,SAAA;MACA,IAAAC,gBAAA,GAAAD,SAAA,CAAA9G,KAAA;QAAAgH,iBAAA,OAAAC,eAAA,CAAAhS,OAAA,EAAA8R,gBAAA;QAAAG,GAAA,GAAAF,iBAAA;QAAAG,IAAA,GAAAH,iBAAA;MACA,KAAAE,GAAA,EAAAC,IAAA,IAAAN,QAAA;IACA;IACA;IACAO,YAAA,WAAAA,aAAAR,IAAA,EAAAC,QAAA,EAAAC,SAAA;MACA,IAAAO,iBAAA,GAAAP,SAAA,CAAA9G,KAAA;QAAAsH,iBAAA,OAAAL,eAAA,CAAAhS,OAAA,EAAAoS,iBAAA;QAAAH,GAAA,GAAAI,iBAAA;QAAAH,IAAA,GAAAG,iBAAA;MACA,KAAAJ,GAAA,EAAAC,IAAA,IAAAN,QAAA;IACA;IACA;IACAU,iBAAA,WAAAA,kBAAA;MACA,KAAAtL,MAAA,CAAAE,QAAA;MACA,KAAAF,MAAA,CAAAuL,YAAA;IACA;IACAC,wBAAA,WAAAA,yBAAAb,IAAA;MACA,KAAA7O,cAAA,GAAA6O,IAAA,CAAAc,GAAA;MACA,KAAA1P,aAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}