{"ast": null, "code": "\"use strict\";\n\nvar _interopRequireDefault = require(\"D:/code_project/java_project/loan/post-loan-backend-page/node_modules/@babel/runtime/helpers/interopRequireDefault.js\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.RevokeVm_car_order = RevokeVm_car_order;\nexports.addVm_car_order = addVm_car_order;\nexports.delVm_car_order = delVm_car_order;\nexports.getVm_car_order = getVm_car_order;\nexports.listVm_car_order = listVm_car_order;\nexports.mailKey = mailKey;\nexports.submitCarFindCost = submitCarFindCost;\nexports.teamVm_car_order = teamVm_car_order;\nexports.updateVm_car_order = updateVm_car_order;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\n// 查询VIEW列表\nfunction listVm_car_order(query) {\n  return (0, _request.default)({\n    url: '/vm_car_order/vm_car_order/list',\n    method: 'get',\n    params: query\n  });\n}\n// 查询录单渠道、找车团队\nfunction teamVm_car_order() {\n  return (0, _request.default)({\n    url: '/vm_car_order/vm_car_order/cate',\n    method: 'get'\n  });\n}\n\n// 查询VIEW详细\nfunction getVm_car_order(id) {\n  return (0, _request.default)({\n    url: '/vm_car_order/vm_car_order/' + id,\n    method: 'get'\n  });\n}\n\n// 新增VIEW\nfunction addVm_car_order(data) {\n  return (0, _request.default)({\n    url: '/vm_car_order/vm_car_order',\n    method: 'post',\n    data: data\n  });\n}\n\n// 修改VIEW\nfunction updateVm_car_order(data) {\n  return (0, _request.default)({\n    url: '/vm_car_order/vm_car_order',\n    method: 'put',\n    data: data\n  });\n}\n\n// 删除VIEW\nfunction delVm_car_order(id) {\n  return (0, _request.default)({\n    url: '/vm_car_order/vm_car_order/' + id,\n    method: 'delete'\n  });\n}\n// 撤销订单\nfunction RevokeVm_car_order(query) {\n  return (0, _request.default)({\n    url: '/car_order/car_order',\n    method: 'put',\n    data: query\n  });\n}\n\n// 提交找车费用\nfunction submitCarFindCost(data) {\n  return (0, _request.default)({\n    url: '/vm_car_order/vm_car_order/submitCost',\n    method: 'post',\n    data: data\n  });\n}\n\n// 邮寄钥匙\nfunction mailKey(data) {\n  return (0, _request.default)({\n    url: '/vm_car_order/vm_car_order/mailKey',\n    method: 'post',\n    data: data\n  });\n}", "map": {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listVm_car_order", "query", "request", "url", "method", "params", "teamVm_car_order", "getVm_car_order", "id", "addVm_car_order", "data", "updateVm_car_order", "delVm_car_order", "RevokeVm_car_order", "submitCarFindCost", "mailKey"], "sources": ["D:/code_project/java_project/loan/post-loan-backend-page/src/api/vm_car_order/vm_car_order.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询VIEW列表\r\nexport function listVm_car_order(query) {\r\n  return request({\r\n    url: '/vm_car_order/vm_car_order/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n// 查询录单渠道、找车团队\r\nexport function teamVm_car_order() {\r\n  return request({\r\n    url: '/vm_car_order/vm_car_order/cate',\r\n    method: 'get',\r\n  })\r\n}\r\n\r\n// 查询VIEW详细\r\nexport function getVm_car_order(id) {\r\n  return request({\r\n    url: '/vm_car_order/vm_car_order/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增VIEW\r\nexport function addVm_car_order(data) {\r\n  return request({\r\n    url: '/vm_car_order/vm_car_order',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改VIEW\r\nexport function updateVm_car_order(data) {\r\n  return request({\r\n    url: '/vm_car_order/vm_car_order',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除VIEW\r\nexport function delVm_car_order(id) {\r\n  return request({\r\n    url: '/vm_car_order/vm_car_order/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n// 撤销订单\r\nexport function RevokeVm_car_order(query) {\r\n  return request({\r\n    url: '/car_order/car_order',\r\n    method: 'put',\r\n    data: query\r\n  })\r\n}\r\n\r\n// 提交找车费用\r\nexport function submitCarFindCost(data) {\r\n  return request({\r\n    url: '/vm_car_order/vm_car_order/submitCost',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 邮寄钥匙\r\nexport function mailKey(data) {\r\n  return request({\r\n    url: '/vm_car_order/vm_car_order/mailKey',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASK,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B,GAAGK,EAAE;IACvCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,eAAeA,CAACC,IAAI,EAAE;EACpC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,kBAAkBA,CAACD,IAAI,EAAE;EACvC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,eAAeA,CAACJ,EAAE,EAAE;EAClC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B,GAAGK,EAAE;IACvCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASS,kBAAkBA,CAACZ,KAAK,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAET;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,iBAAiBA,CAACJ,IAAI,EAAE;EACtC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACL,IAAI,EAAE;EAC5B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}